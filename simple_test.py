#!/usr/bin/env python3
"""
Simple test of the API
"""
import requests

try:
    print("Testing basic API...")
    response = requests.get("http://localhost:5000/api/clusters", timeout=5)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Clusters: {len(data)}")
        for cluster in data:
            print(f"  - {cluster['id']}: {cluster['name']} ({cluster['host']})")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Error: {e}")
