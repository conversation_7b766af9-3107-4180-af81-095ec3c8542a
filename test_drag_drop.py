#!/usr/bin/env python3
"""
Test the drag-and-drop functionality
"""
import requests
import time

def test_drag_drop():
    """Test the drag-and-drop queue reordering"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Drag-and-Drop Queue Reordering...")
    print("=" * 50)
    
    # Get cluster and queue jobs
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        print(f"✅ Using cluster: {clusters[0]['name']} ({cluster_id})")
        
        # Get current queue
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to get queue jobs: {response.status_code}")
            return
            
        queue_jobs = response.json()
        print(f"✅ Found {len(queue_jobs)} queue jobs")
        
        if len(queue_jobs) < 2:
            print("ℹ️  Need at least 2 jobs in queue to test reordering")
            return
            
        print("\n📋 Current queue order:")
        for i, job in enumerate(queue_jobs):
            print(f"   {i+1}. Job {job['job_id']}: {job['name']} (Priority: {job.get('priority', 'N/A')})")
        
        # Test moving job from position 4 to position 2 (as mentioned in the issue)
        if len(queue_jobs) >= 4:
            print(f"\n🎯 Testing: Move job 4 to position 2")
            
            # Simulate the drag-and-drop reordering
            # Original order: [job1, job2, job3, job4, ...]
            # New order after drag: [job1, job4, job2, job3, ...]
            
            original_order = [job['job_id'] for job in queue_jobs]
            print(f"   Original order: {original_order[:4]}")
            
            # Move job at index 3 (4th job) to index 1 (2nd position)
            new_order = original_order.copy()
            job_to_move = new_order.pop(3)  # Remove job from position 4
            new_order.insert(1, job_to_move)  # Insert at position 2
            
            print(f"   New order: {new_order[:4]}")
            print(f"   Job moved: {job_to_move}")
            
            # Test the set_top_priority endpoint with the moved job
            print(f"\n🔧 Testing set_top_priority for job: {job_to_move}")
            
            response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                                   json={'job_id': job_to_move}, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"   ✅ Successfully moved job to top: {result['message']}")
                else:
                    print(f"   ❌ Failed to move job: {result['error']}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
        
        else:
            print(f"\n🔧 Testing with available jobs (only {len(queue_jobs)} jobs)")
            test_job = queue_jobs[0]['job_id']
            
            response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                                   json={'job_id': test_job}, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"   ✅ Successfully moved job to top: {result['message']}")
                else:
                    print(f"   ❌ Failed to move job: {result['error']}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
        
        print(f"\n💡 Dashboard Instructions:")
        print(f"   1. Open: http://localhost:5000")
        print(f"   2. Look for the yellow 'Job Queue - Priority Management' section")
        print(f"   3. Try dragging a job card up or down")
        print(f"   4. The job should stay in the new position")
        print(f"   5. Check browser console (F12) for drag events")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_drag_drop()
