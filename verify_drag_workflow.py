#!/usr/bin/env python3
"""
Verify the exact drag and drop workflow
"""
import requests

def verify_drag_workflow():
    """Verify the drag and drop workflow step by step"""
    
    base_url = "http://localhost:5000"
    
    print("VERIFYING DRAG & DROP WORKFLOW")
    print("=" * 50)
    
    try:
        # Get current queue
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        
        # Get current queue jobs
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to get queue jobs: {response.status_code}")
            return
            
        queue_jobs = response.json()
        print(f"✅ Current queue has {len(queue_jobs)} jobs")
        
        if len(queue_jobs) < 2:
            print("❌ Need at least 2 jobs to test drag and drop")
            return
        
        print(f"\n📋 Current queue order:")
        for i, job in enumerate(queue_jobs):
            short_id = job['job_id'].split('_')[0]
            print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')})")
        
        print(f"\n🎯 WORKFLOW VERIFICATION:")
        print(f"   The new JavaScript is loaded and should work as follows:")
        print(f"   ")
        print(f"   1. 🖱️ DRAG: When you drag a job to a new position")
        print(f"      → handlePriorityChange() function is called")
        print(f"      → Console shows: '🖱️ DRAG COMPLETED'")
        print(f"      → Visual order changes immediately")
        print(f"      → Yellow 'Save Order' section appears")
        print(f"   ")
        print(f"   2. 💾 SAVE: When you click 'Save New Order' button")
        print(f"      → saveQueueOrder() function is called")
        print(f"      → Console shows: '🧠 MULTI-JOB REORDERING'")
        print(f"      → Multiple scontrol top commands execute")
        print(f"      → Console shows: '🔄 Step 1/X: scontrol top ...'")
        print(f"      → Dashboard refreshes after 2.5 seconds")
        print(f"   ")
        print(f"   3. ✅ RESULT: Queue order should match your drag arrangement")
        
        print(f"\n🔍 TROUBLESHOOTING CHECKLIST:")
        print(f"   ")
        print(f"   ❓ Are you seeing the drag work visually?")
        print(f"      → Job should move to new position when you drag it")
        print(f"      → If not: Sortable.js library might not be loaded")
        print(f"   ")
        print(f"   ❓ Does the yellow 'Save Order' section appear?")
        print(f"      → Should appear after any drag operation")
        print(f"      → Shows: 'Queue order has been changed!'")
        print(f"      → If not: handlePriorityChange() function not working")
        print(f"   ")
        print(f"   ❓ Are you clicking the 'Save New Order' button?")
        print(f"      → Must click this button to apply changes to SLURM")
        print(f"      → Dragging alone doesn't change SLURM queue")
        print(f"   ")
        print(f"   ❓ Do you see console messages when clicking Save?")
        print(f"      → Open F12 → Console before clicking Save")
        print(f"      → Should see detailed step-by-step messages")
        print(f"      → If not: saveQueueOrder() function not working")
        print(f"   ")
        print(f"   ❓ Are the scontrol commands actually executing?")
        print(f"      → Server logs show POST /api/cluster/.../set_top_priority")
        print(f"      → Each step should return HTTP 200")
        print(f"      → If failing: SLURM permission or command issues")
        
        print(f"\n🎮 EXACT TEST STEPS:")
        print(f"   1. Open: http://localhost:5000")
        print(f"   2. Press Ctrl+F5 to force refresh")
        print(f"   3. Open browser console (F12 → Console)")
        print(f"   4. Drag the LAST job to the SECOND position")
        print(f"   5. Check: Does yellow 'Save Order' section appear?")
        print(f"   6. Click: 'Save New Order' button")
        print(f"   7. Watch: Console for step-by-step messages")
        print(f"   8. Wait: 3 seconds for dashboard to refresh")
        print(f"   9. Verify: Job is now in second position")
        
        print(f"\n⚠️  MOST LIKELY ISSUES:")
        print(f"   1. 🔄 Browser cache - Try Ctrl+F5 or incognito mode")
        print(f"   2. 🖱️ Not clicking 'Save Order' - Drag alone doesn't save")
        print(f"   3. ⏱️ Not waiting for refresh - Takes 2.5 seconds")
        print(f"   4. 📱 JavaScript errors - Check console for red errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    verify_drag_workflow()
