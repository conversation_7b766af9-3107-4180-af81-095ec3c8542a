#!/usr/bin/env python3
"""
Test the new save order functionality
"""
import requests

def test_save_order_functionality():
    """Test the save order functionality"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Save Order Functionality...")
    print("=" * 50)
    
    # Get current queue
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        print(f"✅ Using cluster: {clusters[0]['name']} ({cluster_id})")
        
        # Get current queue
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to get queue jobs: {response.status_code}")
            return
            
        queue_jobs = response.json()
        print(f"✅ Found {len(queue_jobs)} queue jobs")
        
        if len(queue_jobs) < 2:
            print("ℹ️  Need at least 2 jobs in queue to test reordering")
            return
            
        print("\n📋 Current queue order:")
        for i, job in enumerate(queue_jobs):
            print(f"   {i+1}. Job {job['job_id']}: {job['name']} (Priority: {job.get('priority', 'N/A')})")
        
        # Demonstrate the new workflow
        print(f"\n🎯 NEW SAVE ORDER WORKFLOW:")
        print(f"   1. 📋 Original order is saved when queue loads")
        print(f"   2. 🖱️  User drags jobs to new positions (no immediate SLURM commands)")
        print(f"   3. 💾 'Save Order' button appears with change description")
        print(f"   4. 🔄 User clicks 'Save Order' to apply changes to SLURM")
        print(f"   5. ⚡ Multiple 'scontrol top' commands executed in reverse order")
        
        # Example scenario
        if len(queue_jobs) >= 4:
            print(f"\n📝 EXAMPLE SCENARIO:")
            print(f"   Original order: [1, 2, 3, 4]")
            print(f"   User drags job 4 to position 2")
            print(f"   New order: [1, 4, 2, 3]")
            print(f"   ")
            print(f"   💾 Save Order button shows:")
            print(f"   'Job 900185 moved from position 4 to 2'")
            print(f"   ")
            print(f"   🔄 When user clicks Save Order:")
            print(f"   Step 1: scontrol top {queue_jobs[2]['job_id'].split('_')[0]}  # Job 3 to top")
            print(f"   Step 2: scontrol top {queue_jobs[1]['job_id'].split('_')[0]}  # Job 2 to top") 
            print(f"   Step 3: scontrol top {queue_jobs[3]['job_id'].split('_')[0]}  # Job 4 to top")
            print(f"   Step 4: scontrol top {queue_jobs[0]['job_id'].split('_')[0]}  # Job 1 to top")
            print(f"   ")
            print(f"   ✅ Final SLURM order: [1, 4, 2, 3]")
        
        print(f"\n🎮 HOW TO TEST:")
        print(f"   1. Open: http://localhost:5000")
        print(f"   2. Look for yellow 'Job Queue - Priority Management' section")
        print(f"   3. Drag any job to a different position")
        print(f"   4. Notice: Yellow 'Save Order' section appears")
        print(f"   5. Click '💾 Save New Order' to apply changes")
        print(f"   6. Or click '❌ Cancel Changes' to revert")
        
        print(f"\n🔧 FEATURES:")
        print(f"   ✅ Drag-and-drop without immediate SLURM commands")
        print(f"   ✅ Visual feedback showing what changed")
        print(f"   ✅ Save/Cancel options for user control")
        print(f"   ✅ 'Top Priority' still works immediately")
        print(f"   ✅ Auto-refresh paused during editing")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_save_order_functionality()
