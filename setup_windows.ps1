# SLURM Dashboard Windows Setup Script

Write-Host "=========================================="
Write-Host "    SLURM Dashboard Windows Setup"
Write-Host "=========================================="

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion"
} catch {
    Write-Host "❌ Python not found. Please install Python 3.7+ from https://python.org"
    exit 1
}

# Check if pip is available
try {
    pip --version | Out-Null
    Write-Host "✅ pip is available"
} catch {
    Write-Host "❌ pip not found. Please ensure pip is installed"
    exit 1
}

# Install Python dependencies
if (Test-Path "requirements.txt") {
    Write-Host "Installing Python dependencies..."
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install dependencies"
        exit 1
    }
    Write-Host "✅ Dependencies installed successfully"
} else {
    Write-Host "⚠️  requirements.txt not found"
}

# Check if SSH client is available (Windows 10+)
try {
    ssh -V 2>&1 | Out-Null
    Write-Host "✅ SSH client is available"
} catch {
    Write-Host "⚠️  SSH client not found. Please enable OpenSSH client:"
    Write-Host "   1. Go to Settings > Apps > Optional Features"
    Write-Host "   2. Click 'Add a feature'"
    Write-Host "   3. Find and install 'OpenSSH Client'"
    Write-Host "   Or use Docker deployment for better compatibility"
}

# Create necessary directories
Write-Host "Creating directories..."
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

# Create .env file if it doesn't exist
if (!(Test-Path ".env")) {
    Write-Host "Creating .env file from template..."
    Copy-Item ".env.example" ".env"
    Write-Host "⚠️  Please edit .env file with your configuration:"
    Write-Host "   - SLURM_HOST: Your SLURM head node hostname/IP"
    Write-Host "   - SLURM_USER: SLURM admin user"
    Write-Host "   - SLURM_SSH_PASSWORD: SSH password for the user"
    Write-Host "   - CLUSTER_NAME: Your cluster name"
}

Write-Host ""
Write-Host "Setup complete! Next steps:"
Write-Host ""
Write-Host "1. Edit .env file with your SLURM host details"
Write-Host "2. Test SSH connection manually:"
Write-Host "   ssh slurmadmin@your-slurm-host"
Write-Host ""
Write-Host "3. Start the dashboard:"
Write-Host "   python main.py"
Write-Host ""
Write-Host "4. For better compatibility, consider Docker deployment:"
Write-Host "   docker-compose up --build"
Write-Host ""
Write-Host "Dashboard will be available at: http://localhost:5000"
Write-Host "=========================================="
