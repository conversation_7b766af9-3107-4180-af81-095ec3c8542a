#!/usr/bin/env python3
"""
Test the queue reordering functionality
"""
import requests
import json

def test_queue_reordering():
    """Test the queue reordering with scontrol top logic"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Queue Reordering Logic...")
    print("=" * 50)
    
    # Test 1: Get current queue
    print("1. Getting current queue jobs...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("   ❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        print(f"   Using cluster: {clusters[0]['name']} ({cluster_id})")
        
        # Get queue jobs
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code == 200:
            queue_jobs = response.json()
            print(f"   ✅ Found {len(queue_jobs)} queue jobs")
            
            if queue_jobs:
                print("   Current queue order:")
                for i, job in enumerate(queue_jobs[:5]):  # Show first 5
                    print(f"     {i+1}. Job {job['job_id']}: {job['name']} (Priority: {job.get('priority', 'N/A')})")
            else:
                print("   ℹ️  No jobs in queue to test reordering")
                return
        else:
            print(f"   ❌ Failed to get queue jobs: {response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Test 2: Test the set_top_priority endpoint
    if queue_jobs:
        print(f"\n2. Testing set_top_priority endpoint...")
        test_job = queue_jobs[0]  # Use first job for testing
        job_id = test_job['job_id']
        
        try:
            print(f"   Testing with job {job_id}...")
            
            response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                                   json={'job_id': job_id}, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"   ✅ Successfully moved job to top: {result['message']}")
                else:
                    print(f"   ❌ Failed to move job to top: {result['error']}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test 3: Explain the reordering logic
    print(f"\n3. Queue Reordering Logic Explanation:")
    print("   📋 How SLURM 'scontrol top' works:")
    print("      • Each 'scontrol top <job_id>' moves that job to position 1")
    print("      • All other jobs get pushed down by 1 position")
    print("   ")
    print("   🎯 To achieve desired order [A, B, C, D]:")
    print("      • Apply 'scontrol top' in REVERSE order: [D, C, B, A]")
    print("      • Step 1: 'scontrol top D' → [D, ...]")
    print("      • Step 2: 'scontrol top C' → [C, D, ...]")
    print("      • Step 3: 'scontrol top B' → [B, C, D, ...]")
    print("      • Step 4: 'scontrol top A' → [A, B, C, D, ...]")
    print("   ")
    print("   ✅ Result: Jobs are now in the desired order!")
    
    print(f"\n4. Dashboard Implementation:")
    print("   🖱️  User drags jobs to desired order in the UI")
    print("   📝 JavaScript captures the new order array")
    print("   🔄 Applies 'scontrol top' commands in reverse order")
    print("   ⏱️  500ms delay between commands for proper sequencing")
    print("   🔄 Refreshes data to show the new queue order")

if __name__ == "__main__":
    test_queue_reordering()
