#!/usr/bin/env python3
"""
Test the enhanced dashboard with cluster management
"""
import requests

def test_enhanced_dashboard():
    """Test the enhanced dashboard functionality"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Enhanced Dashboard with Cluster Management...")
    print("=" * 60)
    
    # Test 1: Main page
    print("1. Testing main dashboard page...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            content = response.text
            if 'Priority Management' in content:
                print(f"   ✅ Enhanced dashboard loaded")
            if 'Manage Clusters' in content:
                print(f"   ✅ Cluster management buttons present")
            if 'sortablejs' in content:
                print(f"   ✅ Drag-and-drop library loaded")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Cluster list API
    print("\n2. Testing cluster management API...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            clusters = response.json()
            print(f"   ✅ Found {len(clusters)} clusters")
            for cluster in clusters:
                print(f"     - {cluster['id']}: {cluster['name']} ({cluster['host']})")
                print(f"       Status: {cluster['status']}, Jobs: {cluster['running_count']} running, {cluster['queue_count']} queued")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Test connection API
    print("\n3. Testing connection test API...")
    try:
        test_data = {
            "host": "************",
            "username": "nvme",
            "password": "logan",
            "port": 22
        }
        
        response = requests.post(f"{base_url}/api/test-connection", 
                               json=test_data, timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"   ✅ Connection test successful: {result['message']}")
                if result.get('slurm_version'):
                    print(f"   ✅ SLURM version: {result['slurm_version']}")
            else:
                print(f"   ❌ Connection test failed: {result['error']}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_enhanced_dashboard()
