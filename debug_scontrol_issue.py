#!/usr/bin/env python3
"""
Debug the scontrol top command issue
"""
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import ClusterManager

def debug_scontrol_issue():
    """Debug why scontrol top is removing jobs from queue"""
    
    print("DEBUGGING SCONTROL TOP COMMAND ISSUE")
    print("=" * 50)
    
    # Initialize cluster manager
    cluster_manager = ClusterManager()
    
    if not cluster_manager.clusters:
        print("❌ No clusters found")
        return
    
    # Get the first cluster
    cluster_id = list(cluster_manager.clusters.keys())[0]
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"✅ Testing cluster: {manager.cluster_name}")
    
    # Get current queue
    print(f"\n1. Getting current queue...")
    queue_jobs = manager.get_queue_jobs()
    
    if len(queue_jobs) == 0:
        print("❌ No jobs in queue to test")
        return
    
    print(f"📋 Current queue order:")
    for i, job in enumerate(queue_jobs):
        short_id = job['job_id'].split('_')[0]
        print(f"   {i+1}. Job {short_id} (Full: {job['job_id']}) - Priority: {job.get('priority', 'N/A')}")
    
    # Test with the last job
    test_job = queue_jobs[-1]
    test_job_id = test_job['job_id']
    test_job_short = test_job_id.split('_')[0]
    
    print(f"\n2. TESTING WITH JOB {test_job_short}")
    print(f"   Full job ID: {test_job_id}")
    print(f"   Current priority: {test_job.get('priority', 'N/A')}")
    
    # Check job details before command
    print(f"\n3. JOB DETAILS BEFORE COMMAND:")
    show_command = f"scontrol show job {test_job_short}"
    job_details_before = manager.run_command(show_command)
    
    if job_details_before:
        print(f"   Job exists and details available")
        # Look for key fields
        lines = job_details_before.split('\\n')
        for line in lines:
            if any(keyword in line for keyword in ['JobState', 'Priority', 'Reason', 'Dependency']):
                print(f"      {line.strip()}")
    else:
        print(f"   ❌ Cannot get job details")
        return
    
    # Execute scontrol top command
    print(f"\n4. EXECUTING SCONTROL TOP COMMAND:")
    top_command = f"sudo scontrol top {test_job_short}"
    print(f"   Command: {top_command}")
    
    output = manager.run_command(top_command)
    print(f"   Output: '{output}'")
    
    if output is None:
        print(f"   ❌ Command failed")
        return
    
    # Wait a moment
    print(f"\n5. WAITING 3 SECONDS FOR SLURM TO UPDATE...")
    time.sleep(3)
    
    # Check job details after command
    print(f"\n6. JOB DETAILS AFTER COMMAND:")
    job_details_after = manager.run_command(show_command)
    
    if job_details_after:
        print(f"   Job still exists")
        # Look for changes
        lines = job_details_after.split('\\n')
        for line in lines:
            if any(keyword in line for keyword in ['JobState', 'Priority', 'Reason', 'Dependency']):
                print(f"      {line.strip()}")
    else:
        print(f"   ❌ Job no longer exists or cannot get details")
    
    # Check new queue
    print(f"\n7. QUEUE AFTER COMMAND:")
    new_queue = manager.get_queue_jobs()
    
    print(f"   📋 New queue order:")
    target_found = False
    target_position = None
    
    for i, job in enumerate(new_queue):
        short_id = job['job_id'].split('_')[0]
        marker = ""
        if job['job_id'] == test_job_id:
            marker = " ← TARGET JOB"
            target_found = True
            target_position = i + 1
        print(f"      {i+1}. Job {short_id}{marker}")
    
    print(f"\n8. ANALYSIS:")
    if target_found:
        print(f"   ✅ Job {test_job_short} found at position {target_position}")
        if target_position == 1:
            print(f"   ✅ SUCCESS: Job moved to top as expected")
        else:
            print(f"   ⚠️  Job moved but not to top (position {target_position})")
    else:
        print(f"   ❌ CRITICAL: Job {test_job_short} NOT FOUND in queue!")
        print(f"   🔍 This explains why jobs appear to 'go to last position'")
        print(f"   💡 The job is being removed from the queue entirely")
        
        # Check if job is running instead
        print(f"\n9. CHECKING IF JOB IS NOW RUNNING:")
        running_jobs = manager.get_running_jobs()
        
        job_found_running = False
        for job in running_jobs:
            if job['job_id'] == test_job_id:
                job_found_running = True
                print(f"   ✅ Job {test_job_short} is now RUNNING!")
                print(f"   💡 scontrol top moved it from queue to running state")
                break
        
        if not job_found_running:
            print(f"   ❌ Job {test_job_short} not found in running jobs either")
            print(f"   🔍 Job may have been cancelled or failed")
        
        # Check all jobs to see if it exists anywhere
        print(f"\n10. CHECKING ALL JOBS:")
        all_jobs_command = "squeue -o '%i|%j|%u|%T|%P' --noheader"
        all_jobs_output = manager.run_command(all_jobs_command)
        
        if all_jobs_output:
            job_found_anywhere = False
            for line in all_jobs_output.split('\\n'):
                if line.strip() and test_job_short in line:
                    job_found_anywhere = True
                    print(f"   ✅ Job found: {line.strip()}")
                    break
            
            if not job_found_anywhere:
                print(f"   ❌ Job {test_job_short} completely disappeared from SLURM")
    
    print(f"\n11. CONCLUSION:")
    if target_found and target_position == 1:
        print(f"   ✅ scontrol top is working correctly")
        print(f"   🔍 The dashboard issue must be elsewhere")
    elif target_found:
        print(f"   ⚠️  scontrol top moved job but not to position 1")
        print(f"   🔍 There may be higher priority jobs or dependencies")
    else:
        print(f"   ❌ scontrol top is removing jobs from queue")
        print(f"   🔍 This is the root cause of your issue")
        print(f"   💡 Jobs aren't 'going to last position' - they're disappearing!")

if __name__ == "__main__":
    debug_scontrol_issue()
