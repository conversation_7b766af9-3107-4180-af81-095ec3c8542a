#!/usr/bin/env python3
"""
Debug the scontrol commands directly to see what's happening
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import ClusterManager

def debug_scontrol_directly():
    """Debug the scontrol commands directly"""
    
    print("DEBUGGING SCONTROL COMMANDS DIRECTLY")
    print("=" * 50)
    
    # Initialize cluster manager
    cluster_manager = ClusterManager()
    
    if not cluster_manager.clusters:
        print("❌ No clusters found")
        return
    
    # Get the first cluster
    cluster_id = list(cluster_manager.clusters.keys())[0]
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"✅ Testing cluster: {manager.cluster_name} ({manager.slurm_host})")
    print(f"   User: {manager.config.SLURM_USER}")
    
    # Get current queue
    print(f"\n1. Getting current queue...")
    try:
        queue_jobs = manager.get_queue_jobs()
        print(f"   ✅ Found {len(queue_jobs)} queue jobs")
        
        if len(queue_jobs) == 0:
            print("   ❌ No queue jobs to test with")
            return
        
        print(f"\n📋 Current queue order:")
        for i, job in enumerate(queue_jobs):
            short_id = job['job_id'].split('_')[0]
            print(f"   {i+1}. Job {short_id} (Full: {job['job_id']}) - Priority: {job.get('priority', 'N/A')}")
        
        # Test with the last job (should be easiest to see the change)
        test_job = queue_jobs[-1]
        test_job_id = test_job['job_id']
        test_job_short = test_job_id.split('_')[0]
        
        print(f"\n2. Testing scontrol top with job {test_job_short}...")
        print(f"   Full job ID: {test_job_id}")
        print(f"   Current position: {len(queue_jobs)}")
        print(f"   Current priority: {test_job.get('priority', 'N/A')}")
        
        # Execute the raw scontrol command
        scontrol_command = f"sudo scontrol top {test_job_short}"
        print(f"\n🔄 Executing: {scontrol_command}")
        
        output = manager.run_command(scontrol_command)
        print(f"   Command output: '{output}'")
        
        if output is None:
            print(f"   ❌ Command failed - returned None")
            return
        elif output.strip() == "":
            print(f"   ✅ Command succeeded - no output (normal for scontrol)")
        else:
            print(f"   ⚠️  Command output: {output}")
        
        # Wait a moment for SLURM to update
        print(f"\n3. Waiting 2 seconds for SLURM to update...")
        import time
        time.sleep(2)
        
        # Get new queue order
        print(f"\n4. Getting new queue order...")
        new_queue_jobs = manager.get_queue_jobs()
        
        print(f"\n📋 New queue order:")
        for i, job in enumerate(new_queue_jobs):
            short_id = job['job_id'].split('_')[0]
            marker = " ← MOVED!" if job['job_id'] == test_job_id and i == 0 else ""
            print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')}){marker}")
        
        # Check if it worked
        if new_queue_jobs[0]['job_id'] == test_job_id:
            print(f"\n✅ SUCCESS: Job {test_job_short} moved to position 1!")
        else:
            print(f"\n❌ FAILED: Job {test_job_short} did not move to position 1")
            
            # Find where it ended up
            new_position = None
            for i, job in enumerate(new_queue_jobs):
                if job['job_id'] == test_job_id:
                    new_position = i + 1
                    break
            
            if new_position:
                print(f"   Job {test_job_short} is still at position {new_position}")
            else:
                print(f"   Job {test_job_short} not found in queue!")
            
            # Check if priority changed
            old_priority = test_job.get('priority', 'N/A')
            new_job = next((job for job in new_queue_jobs if job['job_id'] == test_job_id), None)
            new_priority = new_job.get('priority', 'N/A') if new_job else 'N/A'
            
            print(f"   Old priority: {old_priority}")
            print(f"   New priority: {new_priority}")
            
            if old_priority != new_priority:
                print(f"   ✅ Priority changed - scontrol command was executed")
                print(f"   ❓ But job didn't move to top - might be SLURM policy issue")
            else:
                print(f"   ❌ Priority unchanged - scontrol command may have failed")
        
        # Test if we have permission issues
        print(f"\n5. Testing SLURM permissions...")
        
        # Test scontrol show job
        show_command = f"scontrol show job {test_job_short}"
        print(f"   Testing: {show_command}")
        show_output = manager.run_command(show_command)
        
        if show_output and "JobId=" in show_output:
            print(f"   ✅ Can read job details")
            
            # Look for Priority in the output
            if "Priority=" in show_output:
                priority_line = [line for line in show_output.split() if "Priority=" in line][0]
                print(f"   Current priority from scontrol: {priority_line}")
        else:
            print(f"   ❌ Cannot read job details: {show_output}")
        
        # Test basic scontrol access
        version_command = "scontrol --version"
        print(f"   Testing: {version_command}")
        version_output = manager.run_command(version_command)
        
        if version_output:
            print(f"   ✅ SLURM version: {version_output.strip()}")
        else:
            print(f"   ❌ Cannot get SLURM version")
        
        # Test sudo access
        sudo_test = "sudo -n true"
        print(f"   Testing: {sudo_test}")
        sudo_output = manager.run_command(sudo_test)
        
        if sudo_output is not None:
            print(f"   ✅ Sudo access working")
        else:
            print(f"   ❌ Sudo access failed")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_scontrol_directly()
