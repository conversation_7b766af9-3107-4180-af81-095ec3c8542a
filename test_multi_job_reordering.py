#!/usr/bin/env python3
"""
Test the improved multi-job reordering logic
"""

def demonstrate_multi_job_reordering():
    """Demonstrate the improved multi-job reordering logic"""
    
    print("IMPROVED MULTI-JOB REORDERING LOGIC")
    print("=" * 50)
    
    # Your scenario: Move job 3 to position 2 AND job 4 to position 3
    print("📋 YOUR SCENARIO:")
    original_order = ["Job1", "Job2", "Job3", "Job4", "Job5", "Job6"]
    print(f"Original order: {original_order}")
    print(f"   Position 1: Job1")
    print(f"   Position 2: Job2")
    print(f"   Position 3: Job3 ← Move to position 2")
    print(f"   Position 4: Job4 ← Move to position 3")
    print(f"   Position 5: Job5")
    print(f"   Position 6: Job6")
    
    # After your drag operations
    desired_order = ["Job1", "Job3", "Job4", "Job2", "Job5", "Job6"]
    print(f"\nDesired order: {desired_order}")
    print(f"   Position 1: Job1 (unchanged)")
    print(f"   Position 2: Job3 (moved from position 3)")
    print(f"   Position 3: Job4 (moved from position 4)")
    print(f"   Position 4: Job2 (moved from position 2)")
    print(f"   Position 5: Job5 (unchanged)")
    print(f"   Position 6: Job6 (unchanged)")
    
    print(f"\n🧠 IMPROVED LOGIC:")
    print(f"   Detects: Multiple jobs moved - use precise reverse-order approach")
    print(f"   Strategy: Execute scontrol top in reverse order of desired arrangement")
    
    # Show the reverse order commands
    reverse_order = desired_order[::-1]
    print(f"\n🔄 SCONTROL TOP COMMANDS (in reverse order):")
    for i, job in enumerate(reverse_order):
        step = i + 1
        print(f"   Step {step}: scontrol top {job}")
    
    # Simulate the execution
    print(f"\n📋 SIMULATION OF EXECUTION:")
    current_queue = original_order.copy()
    print(f"   Starting queue: {current_queue}")
    
    for i, job in enumerate(reverse_order):
        step = i + 1
        # Simulate scontrol top: move job to position 1
        if job in current_queue:
            current_queue.remove(job)
            current_queue.insert(0, job)
        print(f"   After step {step} (top {job}): {current_queue}")
    
    print(f"\n✅ FINAL RESULT:")
    print(f"   Desired: {desired_order}")
    print(f"   Actual:  {current_queue}")
    print(f"   Match: {'✅ YES' if current_queue == desired_order else '❌ NO'}")

def test_your_exact_scenario():
    """Test your exact scenario with real job IDs"""
    
    print(f"\n" + "=" * 50)
    print("TESTING YOUR EXACT SCENARIO")
    print("=" * 50)
    
    # Current queue (from the logs)
    current_queue = [
        "899983",  # Position 1 (moved to top in previous test)
        "900186",  # Position 2
        "900202",  # Position 3 ← You want to move to position 2
        "900199",  # Position 4 ← You want to move to position 3
        "900185",  # Position 5
        "900184"   # Position 6
    ]
    
    print(f"📋 Current queue: {current_queue}")
    print(f"   You want to:")
    print(f"   • Move 900202 from position 3 to position 2")
    print(f"   • Move 900199 from position 4 to position 3")
    
    # Your desired order
    desired_queue = ["899983", "900202", "900199", "900186", "900185", "900184"]
    print(f"\n🎯 Desired order: {desired_queue}")
    
    # Show the commands that will be executed
    reverse_order = desired_queue[::-1]
    print(f"\n🔄 Commands that will be executed:")
    for i, job in enumerate(reverse_order):
        step = i + 1
        print(f"   Step {step}: scontrol top {job}")
    
    # Simulate execution
    print(f"\n📋 Simulation:")
    sim_queue = current_queue.copy()
    print(f"   Start: {sim_queue}")
    
    for i, job in enumerate(reverse_order):
        step = i + 1
        if job in sim_queue:
            sim_queue.remove(job)
            sim_queue.insert(0, job)
        print(f"   Step {step}: {sim_queue}")
    
    print(f"\n✅ RESULT:")
    print(f"   Desired: {desired_queue}")
    print(f"   Simulated: {sim_queue}")
    print(f"   Match: {'✅ PERFECT' if sim_queue == desired_queue else '❌ ERROR'}")
    
    print(f"\n🎮 HOW TO TEST:")
    print(f"   1. Open: http://localhost:5000")
    print(f"   2. Open browser console (F12 → Console)")
    print(f"   3. Drag job 900202 from position 3 to position 2")
    print(f"   4. Drag job 900199 from position 4 to position 3")
    print(f"   5. Click 'Save New Order'")
    print(f"   6. Watch console for:")
    print(f"      - 🧠 MULTI-JOB REORDERING messages")
    print(f"      - 🔄 Step-by-step scontrol commands")
    print(f"      - ✅ Success confirmations")
    
    print(f"\n🎯 EXPECTED CONSOLE OUTPUT:")
    print(f"   🧠 Calculating moves for multi-job reordering...")
    print(f"   Original order: [899983, 900186, 900202, 900199, 900185, 900184]")
    print(f"   Desired order: [899983, 900202, 900199, 900186, 900185, 900184]")
    print(f"   🎯 Using REVERSE ORDER approach for precise reordering")
    print(f"   Commands will be executed in this order:")
    print(f"     Step 1: scontrol top 900184")
    print(f"     Step 2: scontrol top 900185")
    print(f"     Step 3: scontrol top 900186")
    print(f"     Step 4: scontrol top 900199")
    print(f"     Step 5: scontrol top 900202")
    print(f"     Step 6: scontrol top 899983")

if __name__ == "__main__":
    demonstrate_multi_job_reordering()
    test_your_exact_scenario()
