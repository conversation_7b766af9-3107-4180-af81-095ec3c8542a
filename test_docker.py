#!/usr/bin/env python3
"""
Docker Deployment Test Script
Tests Docker setup and connectivity
"""

import subprocess
import os
import sys

def test_docker():
    """Test Docker installation"""
    print("Testing Docker installation...")
    try:
        result = subprocess.run(['docker', '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Docker: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Docker not found")
        return False

def test_docker_compose():
    """Test Docker Compose installation"""
    print("Testing Docker Compose installation...")
    try:
        result = subprocess.run(['docker-compose', '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Docker Compose: {result.stdout.strip()}")
            return True
        else:
            # Try docker compose (newer syntax)
            result = subprocess.run(['docker', 'compose', 'version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Docker Compose: {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker Compose not working properly")
                return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Docker Compose not found")
        return False

def test_files():
    """Test required files exist"""
    print("Testing required files...")
    required_files = [
        'Dockerfile',
        'docker-compose.yml',
        '.env.example',
        'app.py',
        'slurm_manager.py',
        'config.py'
    ]

    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - exists")
        else:
            print(f"❌ {file} - missing")
            all_exist = False

    return all_exist

def test_env_file():
    """Test environment configuration"""
    print("Testing environment configuration...")

    if os.path.exists('.env'):
        print("✅ .env file exists")

        # Read and validate key settings
        with open('.env', 'r') as f:
            content = f.read()

        required_vars = ['SLURM_HOST', 'SLURM_USER', 'CLUSTER_NAME']
        for var in required_vars:
            if var in content:
                print(f"✅ {var} - configured")
            else:
                print(f"⚠️  {var} - not configured")

        return True
    else:
        print("⚠️  .env file not found (will use defaults)")
        print("   Run: copy .env.example .env")
        return False

def test_ssh_setup():
    """Test SSH password setup"""
    print("Testing SSH password setup...")

    # Check if password is configured in environment
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            content = f.read()

        if 'SLURM_SSH_PASSWORD=' in content:
            # Check if password is not empty
            for line in content.split('\n'):
                if line.startswith('SLURM_SSH_PASSWORD=') and '=' in line:
                    password_value = line.split('=', 1)[1].strip()
                    if password_value and password_value != 'your-ssh-password':
                        print("✅ SSH password configured in .env")
                        return True
                    else:
                        print("⚠️  SSH password not set in .env file")
                        print("   Set SLURM_SSH_PASSWORD=your-actual-password")
                        return False
        else:
            print("⚠️  SLURM_SSH_PASSWORD not found in .env")
            return False
    else:
        print("⚠️  .env file not found")
        print("   Create .env file with SSH password configuration")
        return False

def build_test():
    """Test Docker build"""
    print("Testing Docker build...")
    try:
        print("Building Docker image (this may take a few minutes)...")
        result = subprocess.run(['docker-compose', 'build'],
                              capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("✅ Docker build successful")
            return True
        else:
            print("❌ Docker build failed")
            print("Error output:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker build timed out")
        return False
    except Exception as e:
        print(f"❌ Docker build error: {e}")
        return False

def main():
    """Run all tests"""
    print("SLURM Dashboard Docker Test")
    print("=" * 50)

    tests = [
        ("Docker Installation", test_docker),
        ("Docker Compose", test_docker_compose),
        ("Required Files", test_files),
        ("Environment Config", test_env_file),
        ("SSH Password Setup", test_ssh_setup),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        print()

    print("=" * 50)
    print(f"Basic Tests: {passed}/{total} passed")

    if passed >= 4:  # Core requirements met
        print("\n🎉 Core requirements satisfied!")

        # Ask about build test
        if input("\nRun Docker build test? (y/N): ").lower().startswith('y'):
            print("\nDocker Build Test:")
            if build_test():
                print("\n🚀 Ready to deploy!")
                print("\nTo start the dashboard:")
                print("  docker-compose up -d")
            else:
                print("\n⚠️  Build failed. Check Docker configuration.")
        else:
            print("\nSkipping build test.")
            print("\nTo test build manually:")
            print("  docker-compose build")
            print("\nTo start dashboard:")
            print("  docker-compose up -d")
    else:
        print("\n⚠️  Please address the issues above before deploying.")

    print("\nFor detailed setup instructions, see DOCKER_DEPLOYMENT.md")

if __name__ == '__main__':
    main()
