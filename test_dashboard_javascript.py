#!/usr/bin/env python3
"""
Test if the new JavaScript is actually loaded in the dashboard
"""
import requests

def test_dashboard_javascript():
    """Test if the new JavaScript is loaded"""
    
    base_url = "http://localhost:5000"
    
    print("TESTING DASHBOARD JAVASCRIPT")
    print("=" * 50)
    
    try:
        # Get the main dashboard page
        response = requests.get(f"{base_url}/", timeout=5)
        
        if response.status_code != 200:
            print(f"❌ Dashboard not accessible: {response.status_code}")
            return
        
        content = response.text
        
        # Check for key functions in the JavaScript
        checks = [
            ("calculateMinimalMoves", "🧠 Multi-job reordering function"),
            ("MULTI-JOB REORDERING", "🔄 New reordering logic"),
            ("REVERSE ORDER approach", "🎯 Reverse order strategy"),
            ("applyQueueOrder", "💾 Apply queue order function"),
            ("saveQueueOrder", "💾 Save queue order function"),
            ("handlePriorityChange", "🖱️ Drag handler function")
        ]
        
        print("🔍 Checking for new JavaScript functions...")
        
        all_found = True
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}: Found")
            else:
                print(f"   ❌ {description}: NOT FOUND")
                all_found = False
        
        if all_found:
            print(f"\n✅ ALL NEW JAVASCRIPT FUNCTIONS ARE PRESENT!")
        else:
            print(f"\n❌ SOME JAVASCRIPT FUNCTIONS ARE MISSING!")
            print(f"   This means the browser might be using cached JavaScript")
            print(f"   Try: Ctrl+F5 to force refresh, or clear browser cache")
        
        # Check for specific new logic
        if "🧠 Calculating moves for multi-job reordering" in content:
            print(f"\n✅ NEW MULTI-JOB LOGIC CONFIRMED!")
            print(f"   The improved reordering logic is in the dashboard")
        else:
            print(f"\n❌ OLD LOGIC STILL PRESENT!")
            print(f"   The dashboard is still using the old reordering logic")
        
        # Check for console logging
        if "console.log('🧠 MULTI-JOB REORDERING" in content:
            print(f"\n✅ ENHANCED CONSOLE LOGGING PRESENT!")
            print(f"   You should see detailed logs in browser console (F12)")
        else:
            print(f"\n❌ CONSOLE LOGGING MISSING!")
        
        print(f"\n🎮 DEBUGGING STEPS:")
        print(f"   1. Open: http://localhost:5000")
        print(f"   2. Press Ctrl+F5 to force refresh (clear cache)")
        print(f"   3. Open browser console (F12 → Console)")
        print(f"   4. Try dragging a job")
        print(f"   5. Look for these console messages:")
        print(f"      - '🖱️ DRAG COMPLETED'")
        print(f"      - '🧠 MULTI-JOB REORDERING'")
        print(f"      - '🎯 Using REVERSE ORDER approach'")
        print(f"   6. If you don't see these messages, the JavaScript isn't updated")
        
        # Check file size as indicator
        js_size = len(content)
        print(f"\n📊 Dashboard file size: {js_size:,} characters")
        if js_size > 50000:  # The enhanced dashboard should be larger
            print(f"   ✅ File size suggests enhanced dashboard is loaded")
        else:
            print(f"   ⚠️  File size seems small - might be old version")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_dashboard_javascript()
