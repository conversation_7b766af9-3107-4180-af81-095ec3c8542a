#!/usr/bin/env python3
"""
Test the actual scontrol top commands to see what's happening
"""
import requests
import time

def test_scontrol_commands():
    """Test the scontrol top commands directly"""
    
    base_url = "http://localhost:5000"
    
    print("Testing SCONTROL TOP Commands...")
    print("=" * 50)
    
    try:
        # Get cluster info
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        print(f"✅ Using cluster: {clusters[0]['name']} ({cluster_id})")
        
        # Get current queue
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to get queue jobs: {response.status_code}")
            return
            
        queue_jobs = response.json()
        print(f"✅ Found {len(queue_jobs)} queue jobs")
        
        if len(queue_jobs) < 2:
            print("ℹ️  Need at least 2 jobs to test reordering")
            return
        
        print(f"\n📋 Current queue order:")
        for i, job in enumerate(queue_jobs):
            short_id = job['job_id'].split('_')[0]
            print(f"   {i+1}. Job {short_id} (Full: {job['job_id']}) - Priority: {job.get('priority', 'N/A')}")
        
        # Test single scontrol top command
        test_job = queue_jobs[-1]  # Use last job
        test_job_id = test_job['job_id']
        test_job_short = test_job_id.split('_')[0]
        
        print(f"\n🔧 Testing single scontrol top command...")
        print(f"   Job: {test_job_short} (Full: {test_job_id})")
        print(f"   Current position: {len(queue_jobs)}")
        print(f"   Expected: Move to position 1")
        
        # Execute the command
        print(f"\n🔄 Executing: scontrol top {test_job_short}")
        
        response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                               json={'job_id': test_job_id}, timeout=15)
        
        print(f"   HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', 'Unknown')}")
            print(f"   Message: {result.get('message', 'No message')}")
            
            if not result.get('success'):
                print(f"   ❌ Error: {result.get('error', 'Unknown error')}")
                return
        else:
            print(f"   ❌ HTTP Error: {response.text}")
            return
        
        # Wait and check new order
        print(f"\n⏱️  Waiting 3 seconds for SLURM to update...")
        time.sleep(3)
        
        # Get new queue order
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code == 200:
            new_queue_jobs = response.json()
            
            print(f"\n📋 New queue order:")
            for i, job in enumerate(new_queue_jobs):
                short_id = job['job_id'].split('_')[0]
                marker = " ← MOVED!" if job['job_id'] == test_job_id and i == 0 else ""
                print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')}){marker}")
            
            # Check if it worked
            if new_queue_jobs[0]['job_id'] == test_job_id:
                print(f"\n✅ SUCCESS: Job {test_job_short} moved to position 1!")
            else:
                print(f"\n❌ FAILED: Job {test_job_short} is still at position {[job['job_id'] for job in new_queue_jobs].index(test_job_id) + 1}")
                
                # Show what might be wrong
                print(f"\n🔍 DEBUGGING INFO:")
                print(f"   Expected job at position 1: {test_job_short}")
                print(f"   Actual job at position 1: {new_queue_jobs[0]['job_id'].split('_')[0]}")
                
                # Check if priorities changed
                old_priority = test_job.get('priority', 'N/A')
                new_job = next((job for job in new_queue_jobs if job['job_id'] == test_job_id), None)
                new_priority = new_job.get('priority', 'N/A') if new_job else 'N/A'
                
                print(f"   Old priority: {old_priority}")
                print(f"   New priority: {new_priority}")
                
                if old_priority != new_priority:
                    print(f"   ✅ Priority changed - command executed")
                else:
                    print(f"   ❌ Priority unchanged - command may have failed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scontrol_commands()
