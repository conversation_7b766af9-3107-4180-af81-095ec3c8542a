#!/bin/bash

# SLURM Dashboard Docker Setup Script

echo "=========================================="
echo "    SLURM Dashboard Docker Setup"
echo "=========================================="

# Create necessary directories
echo "Creating directories..."
mkdir -p logs

echo "Setting up password-based SSH authentication..."
echo ""
echo "Please ensure you can SSH to your SLURM host with password:"
echo "  ssh slurmadmin@your-slurm-host"
echo ""
echo "If password authentication is disabled, enable it on the SLURM host:"
echo "  1. Edit /etc/ssh/sshd_config"
echo "  2. Set: PasswordAuthentication yes"
echo "  3. Restart SSH service: sudo systemctl restart sshd"
echo ""

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo ""
    echo "⚠️  Please edit .env file with your SLURM host details:"
    echo "   - SLURM_HOST: Your SLURM head node hostname/IP"
    echo "   - SLURM_USER: SLURM admin user"
    echo "   - CLUSTER_NAME: Your cluster name"
fi

echo ""
echo "Setup complete! Next steps:"
echo ""
echo "1. Edit .env file with your configuration"
echo "2. Ensure SSH key is properly configured"
echo "3. Build and run with Docker Compose:"
echo "   docker-compose up --build"
echo ""
echo "Dashboard will be available at: http://localhost:5000"
echo "=========================================="
