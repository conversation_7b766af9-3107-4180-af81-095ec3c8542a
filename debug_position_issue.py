#!/usr/bin/env python3
"""
Debug why jobs are moving to last position instead of intended position
"""
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import ClusterManager

def debug_position_issue():
    """Debug the position issue step by step"""
    
    print("DEBUGGING POSITION ISSUE - JOBS GOING TO LAST POSITION")
    print("=" * 60)
    
    # Initialize cluster manager
    cluster_manager = ClusterManager()
    
    if not cluster_manager.clusters:
        print("❌ No clusters found")
        return
    
    # Get the first cluster
    cluster_id = list(cluster_manager.clusters.keys())[0]
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"✅ Testing cluster: {manager.cluster_name}")
    
    # Get current queue
    print(f"\n1. Getting current queue...")
    queue_jobs = manager.get_queue_jobs()
    
    print(f"📋 Current queue order:")
    for i, job in enumerate(queue_jobs):
        short_id = job['job_id'].split('_')[0]
        print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')})")
    
    if len(queue_jobs) < 4:
        print("❌ Need at least 4 jobs to test position 4 to 3")
        return
    
    # Your exact scenario: Move job 4 to position 3
    job_4 = queue_jobs[3]  # Index 3 = position 4
    job_4_id = job_4['job_id']
    job_4_short = job_4_id.split('_')[0]
    
    print(f"\n2. YOUR EXACT SCENARIO:")
    print(f"   Move job {job_4_short} from position 4 to position 3")
    print(f"   Job ID: {job_4_id}")
    print(f"   Current priority: {job_4.get('priority', 'N/A')}")
    
    # Test 1: Simple scontrol top on job 4
    print(f"\n3. TEST 1: Simple scontrol top {job_4_short}")
    print(f"   This should move job to position 1 (top)")
    
    command = f"sudo scontrol top {job_4_short}"
    print(f"   Executing: {command}")
    
    output = manager.run_command(command)
    if output is None:
        print(f"   ❌ Command failed")
        return
    else:
        print(f"   ✅ Command executed")
    
    # Check result
    time.sleep(2)
    new_queue = manager.get_queue_jobs()
    
    print(f"   📋 Queue after scontrol top:")
    target_found_at = None
    for i, job in enumerate(new_queue):
        short_id = job['job_id'].split('_')[0]
        marker = " ← TARGET" if job['job_id'] == job_4_id else ""
        print(f"      {i+1}. Job {short_id}{marker}")
        if job['job_id'] == job_4_id:
            target_found_at = i + 1
    
    if target_found_at == 1:
        print(f"   ✅ SUCCESS: Job moved to position 1 as expected")
    else:
        print(f"   ❌ PROBLEM: Job is at position {target_found_at}, not position 1")
        print(f"   🔍 This indicates a fundamental SLURM issue")
        
        # Check if job has dependencies or holds
        print(f"\n4. CHECKING JOB DETAILS...")
        show_command = f"scontrol show job {job_4_short}"
        job_details = manager.run_command(show_command)
        
        if job_details:
            print(f"   Job details:")
            # Look for key fields
            lines = job_details.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['Dependency', 'Priority', 'Reason', 'State']):
                    print(f"      {line.strip()}")
        
        return
    
    # Test 2: Try to move it to position 3 using the "correct" logic
    print(f"\n5. TEST 2: Move job to position 3 using reverse order logic")
    
    # Current order after the top command
    current_order = [job['job_id'] for job in new_queue]
    
    # Desired order: job_4 at position 3
    desired_order = current_order.copy()
    # Remove job_4 from current position (should be position 1)
    if job_4_id in desired_order:
        desired_order.remove(job_4_id)
    # Insert at position 2 (index 2 = position 3)
    desired_order.insert(2, job_4_id)
    
    print(f"   Current order: {[job_id.split('_')[0] for job_id in current_order]}")
    print(f"   Desired order: {[job_id.split('_')[0] for job_id in desired_order]}")
    
    # Apply reverse order logic
    reverse_order = desired_order[::-1]
    print(f"   Reverse order commands:")
    for i, job_id in enumerate(reverse_order):
        short_id = job_id.split('_')[0]
        print(f"      Step {i+1}: scontrol top {short_id}")
    
    # Execute the commands
    print(f"\n   Executing reverse order commands...")
    for i, job_id in enumerate(reverse_order):
        short_id = job_id.split('_')[0]
        step = i + 1
        
        command = f"sudo scontrol top {short_id}"
        print(f"   Step {step}: {command}")
        
        output = manager.run_command(command)
        if output is None:
            print(f"      ❌ Failed")
            break
        else:
            print(f"      ✅ Executed")
        
        time.sleep(1)
    
    # Check final result
    print(f"\n6. FINAL RESULT:")
    final_queue = manager.get_queue_jobs()
    
    print(f"   📋 Final queue order:")
    final_target_position = None
    for i, job in enumerate(final_queue):
        short_id = job['job_id'].split('_')[0]
        marker = " ← TARGET" if job['job_id'] == job_4_id else ""
        print(f"      {i+1}. Job {short_id}{marker}")
        if job['job_id'] == job_4_id:
            final_target_position = i + 1
    
    print(f"\n7. ANALYSIS:")
    print(f"   Expected position: 3")
    print(f"   Actual position: {final_target_position}")
    
    if final_target_position == 3:
        print(f"   ✅ SUCCESS: Job is at position 3!")
    elif final_target_position == len(final_queue):
        print(f"   ❌ PROBLEM: Job moved to LAST position")
        print(f"   🔍 This confirms the issue you're experiencing")
        print(f"   💡 The reverse order logic is fundamentally flawed")
    else:
        print(f"   ❌ PROBLEM: Job at unexpected position {final_target_position}")
    
    # Test 3: Try a different approach
    if final_target_position != 3:
        print(f"\n8. TESTING ALTERNATIVE APPROACH:")
        print(f"   Instead of reverse order, try direct positioning")
        
        # Reset: Move target job to top first
        reset_command = f"sudo scontrol top {job_4_short}"
        print(f"   Reset: {reset_command}")
        manager.run_command(reset_command)
        time.sleep(1)
        
        # Get fresh queue
        reset_queue = manager.get_queue_jobs()
        
        # Now move the jobs that should be above position 3
        # For position 3, we need 2 jobs above it
        jobs_above = [job['job_id'] for job in reset_queue[:2] if job['job_id'] != job_4_id]
        
        print(f"   Jobs that should be above position 3: {[job_id.split('_')[0] for job_id in jobs_above]}")
        
        # Move these jobs to top in reverse order
        for job_id in reversed(jobs_above):
            short_id = job_id.split('_')[0]
            command = f"sudo scontrol top {short_id}"
            print(f"   Moving to top: {command}")
            manager.run_command(command)
            time.sleep(1)
        
        # Check result
        alt_queue = manager.get_queue_jobs()
        print(f"\n   Alternative approach result:")
        alt_target_position = None
        for i, job in enumerate(alt_queue):
            short_id = job['job_id'].split('_')[0]
            marker = " ← TARGET" if job['job_id'] == job_4_id else ""
            print(f"      {i+1}. Job {short_id}{marker}")
            if job['job_id'] == job_4_id:
                alt_target_position = i + 1
        
        if alt_target_position == 3:
            print(f"   ✅ ALTERNATIVE APPROACH WORKS!")
            print(f"   💡 Solution: Use direct positioning instead of reverse order")
        else:
            print(f"   ❌ Alternative approach also failed")

if __name__ == "__main__":
    debug_position_issue()
