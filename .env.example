# SLURM Dashboard Environment Configuration

# Optional: Default cluster settings (leave empty to start with no clusters)
# SLURM_HOST=your-slurm-head-node.example.com
# SLURM_USER=slurmadmin
# SLURM_SSH_PASSWORD=your-ssh-password
# CLUSTER_NAME=Production HPC Cluster

# Dashboard Settings
REFRESH_INTERVAL=5
MAX_QUEUE_JOBS=100
MAX_RUNNING_JOBS=50

# Flask Settings
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
HOST=0.0.0.0
PORT=5000

# Docker Settings
DOCKER_MODE=true
LOG_LEVEL=INFO

# SSH Settings
SSH_TIMEOUT=30
