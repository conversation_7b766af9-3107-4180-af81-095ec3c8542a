#!/usr/bin/env python3
"""
Test the main server on port 5000
"""
import requests

def test_main_server():
    """Test the main server"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Main Server (Port 5000)...")
    print("=" * 50)
    
    # Test 1: Get clusters
    print("1. Testing /api/clusters...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            clusters = response.json()
            print(f"   ✅ Found {len(clusters)} clusters")
            for cluster in clusters:
                print(f"     - {cluster['id']}: {cluster['name']} ({cluster['host']})")
                cluster_id = cluster['id']
        else:
            print(f"   ❌ Error: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    if not clusters:
        print("   ℹ️  No clusters found")
        return
    
    # Test 2: Get cluster data
    cluster_id = clusters[0]['id']
    print(f"\n2. Testing cluster data for {cluster_id}...")
    try:
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/data", timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Status: {data.get('status', 'unknown')}")
            print(f"   ✅ Queue jobs: {len(data.get('queue_jobs', []))}")
            print(f"   ✅ Running jobs: {len(data.get('running_jobs', []))}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_main_server()
