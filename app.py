"""
SLURM Dashboard - Main Flask Application
Multi-Cluster Support
"""
from flask import Flask, render_template, jsonify, request
import threading
import time
import uuid
from cluster_manager import <PERSON>lusterManager
from config import Config

app = Flask(__name__)
app.config.from_object(Config)
# DISABLED: SocketIO was causing connection issues
# socketio = SocketIO(app, cors_allowed_origins="*")
# Using regular Flask instead

# Initialize Multi-Cluster manager
cluster_manager = ClusterManager()

# Global data store
dashboard_data = {
    'clusters': {},
    'last_update': None
}

def update_dashboard_data():
    """Update dashboard data from all SLURM clusters - DISABLED"""
    global dashboard_data

    # DISABLED: This function was causing hangs
    # Just update the cluster list without fetching data
    try:
        dashboard_data['cluster_list'] = cluster_manager.get_cluster_list()
        dashboard_data['last_update'] = time.time()
        print("Dashboard data list updated (data fetching disabled)")
    except Exception as e:
        print(f"Error updating dashboard list: {e}")

def background_updater():
    """Background thread to update data periodically"""
    while True:
        try:
            # Only update if there are clusters configured
            if len(cluster_manager.clusters) > 0:
                update_dashboard_data()
            time.sleep(app.config['REFRESH_INTERVAL'])
        except Exception as e:
            print(f"Background updater error: {e}")
            time.sleep(app.config['REFRESH_INTERVAL'])

@app.route('/')
def dashboard():
    """Main dashboard page - enhanced with priority management"""
    return render_template('enhanced_dashboard.html')

@app.route('/minimal')
def minimal_dashboard():
    """Minimal dashboard page"""
    return render_template('minimal_dashboard.html')

@app.route('/original')
def original_dashboard():
    """Original dashboard page"""
    # Don't block on initial load - let the frontend request data
    return render_template('dashboard.html',
                         cluster_name=app.config['CLUSTER_NAME'],
                         refresh_interval=app.config['REFRESH_INTERVAL'])

@app.route('/test')
def test_page():
    """Test page to verify functionality"""
    return render_template('test_page.html')

@app.route('/api/clusters')
def api_clusters():
    """API endpoint for cluster list"""
    return jsonify(cluster_manager.get_cluster_list())

@app.route('/api/cluster/<cluster_id>/queue_jobs')
def api_cluster_queue_jobs(cluster_id):
    """API endpoint for cluster queue jobs - fetch on-demand"""
    try:
        cluster_manager.update_cluster_data(cluster_id)
        data = cluster_manager.get_cluster_data(cluster_id)
        return jsonify(data.get('queue_jobs', []))
    except Exception as e:
        print(f"Error fetching queue jobs for {cluster_id}: {e}")
        return jsonify([])

@app.route('/api/cluster/<cluster_id>/running_jobs')
def api_cluster_running_jobs(cluster_id):
    """API endpoint for cluster running jobs - fetch on-demand"""
    try:
        cluster_manager.update_cluster_data(cluster_id)
        data = cluster_manager.get_cluster_data(cluster_id)
        return jsonify(data.get('running_jobs', []))
    except Exception as e:
        print(f"Error fetching running jobs for {cluster_id}: {e}")
        return jsonify([])

@app.route('/api/cluster/<cluster_id>/info')
def api_cluster_info(cluster_id):
    """API endpoint for cluster information - fetch on-demand"""
    try:
        cluster_manager.update_cluster_data(cluster_id)
        data = cluster_manager.get_cluster_data(cluster_id)
        return jsonify(data.get('cluster_info', {}))
    except Exception as e:
        print(f"Error fetching cluster info for {cluster_id}: {e}")
        return jsonify({})

@app.route('/api/requeue_job', methods=['POST'])
def api_requeue_job_single():
    """API endpoint to requeue a running job (single cluster mode)"""
    try:
        data = request.get_json()
        job_id = data.get('job_id')

        if not job_id:
            return jsonify({'success': False, 'error': 'Missing job_id'})

        # For single cluster mode, use the first available cluster
        if not cluster_manager.clusters:
            return jsonify({'success': False, 'error': 'No clusters configured'})

        cluster_id = list(cluster_manager.clusters.keys())[0]

        # Requeue the job
        success, message = cluster_manager.requeue_job(cluster_id, job_id)

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            })

    except Exception as e:
        print(f"Error requeuing job: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cluster/<cluster_id>/update_priority', methods=['POST'])
def api_update_priority(cluster_id):
    """API endpoint to update job priority"""
    try:
        data = request.get_json()
        job_id = data.get('job_id')
        priority = data.get('priority')

        if not job_id or priority is None:
            return jsonify({'success': False, 'error': 'Missing job_id or priority'})

        # Get cluster manager
        if cluster_id not in cluster_manager.clusters:
            return jsonify({'success': False, 'error': 'Cluster not found'})

        manager = cluster_manager.clusters[cluster_id]

        # Extract base job ID (remove array notation like _[0-215])
        base_job_id = job_id.split('_')[0]

        # Update priority using scontrol
        command = f'sudo scontrol update JobId={base_job_id} Priority={priority}'
        result = manager.run_command(command)

        if result is not None:
            return jsonify({
                'success': True,
                'message': f'Priority updated for job {base_job_id} to {priority}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update priority - check SLURM permissions'
            })

    except Exception as e:
        print(f"Error updating priority for cluster {cluster_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cluster/<cluster_id>/set_top_priority', methods=['POST'])
def api_set_top_priority(cluster_id):
    """API endpoint to set job to top priority using scontrol top"""
    try:
        data = request.get_json()
        job_id = data.get('job_id')

        if not job_id:
            return jsonify({'success': False, 'error': 'Missing job_id'})

        # Get cluster manager
        if cluster_id not in cluster_manager.clusters:
            return jsonify({'success': False, 'error': 'Cluster not found'})

        manager = cluster_manager.clusters[cluster_id]

        # Extract base job ID (remove array notation like _[0-215])
        base_job_id = job_id.split('_')[0]

        # Set top priority using scontrol top
        command = f'sudo scontrol top {base_job_id}'
        result = manager.run_command(command)

        if result is not None:
            return jsonify({
                'success': True,
                'message': f'Job {base_job_id} moved to top priority'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to set top priority - check SLURM permissions'
            })

    except Exception as e:
        print(f"Error setting top priority for cluster {cluster_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cluster/<cluster_id>/requeue_job', methods=['POST'])
def api_requeue_job(cluster_id):
    """API endpoint to requeue a running job"""
    try:
        data = request.get_json()
        job_id = data.get('job_id')

        if not job_id:
            return jsonify({'success': False, 'error': 'Missing job_id'})

        # Get cluster manager
        if cluster_id not in cluster_manager.clusters:
            return jsonify({'success': False, 'error': 'Cluster not found'})

        # Requeue the job
        success, message = cluster_manager.requeue_job(cluster_id, job_id)

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            })

    except Exception as e:
        print(f"Error requeuing job for cluster {cluster_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cluster/<cluster_id>/cancel_job', methods=['POST'])
def api_cancel_job(cluster_id):
    """API endpoint to cancel a running job"""
    try:
        data = request.get_json()
        job_id = data.get('job_id')

        if not job_id:
            return jsonify({'success': False, 'error': 'Missing job_id'})

        # Get cluster manager
        if cluster_id not in cluster_manager.clusters:
            return jsonify({'success': False, 'error': 'Cluster not found'})

        # Cancel the job
        success, message = cluster_manager.cancel_job(cluster_id, job_id)

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            })

    except Exception as e:
        print(f"Error canceling job for cluster {cluster_id}: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/cluster/<cluster_id>/data')
def api_cluster_data(cluster_id):
    """API endpoint for all cluster data - fetch on-demand"""
    try:
        # Check if cluster exists
        if cluster_id not in cluster_manager.clusters:
            return jsonify({'error': 'Cluster not found', 'status': 'error'})

        # Fetch fresh data on-demand
        cluster_manager.update_cluster_data(cluster_id)
        data = cluster_manager.get_cluster_data(cluster_id)

        # Check if data fetch was successful
        if not data:
            return jsonify({'error': 'No data available', 'status': 'error'})

        # Check if there was an error in the data
        if data.get('error'):
            print(f"Cluster {cluster_id} data error: {data['error']}")
            return jsonify({
                'error': data['error'],
                'status': 'error',
                'cluster_id': cluster_id
            })

        # Ensure required fields exist
        if 'queue_jobs' not in data:
            data['queue_jobs'] = []
        if 'running_jobs' not in data:
            data['running_jobs'] = []
        if 'cluster_info' not in data:
            data['cluster_info'] = {}

        print(f"Successfully fetched data for {cluster_id}: {len(data.get('queue_jobs', []))} queue, {len(data.get('running_jobs', []))} running")
        return jsonify(data)

    except Exception as e:
        print(f"Error fetching cluster data for {cluster_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e), 'status': 'error', 'cluster_id': cluster_id})



@app.route('/api/cluster/<cluster_id>/job_details/<job_id>')
def api_job_details(cluster_id, job_id):
    """API endpoint for job details from specific cluster"""
    details = cluster_manager.get_job_details(cluster_id, job_id)
    if details:
        return jsonify(details)
    else:
        return jsonify({'error': 'Job not found'}), 404

# Cluster Management Endpoints
@app.route('/api/clusters', methods=['POST'])
def api_add_cluster():
    """API endpoint to add a new cluster"""
    data = request.get_json()

    required_fields = ['name', 'host', 'username', 'password']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'success': False, 'error': f'Missing required field: {field}'})

    # Generate unique cluster ID
    cluster_id = str(uuid.uuid4())[:8]

    config = {
        'name': data['name'],
        'host': data['host'],
        'username': data['username'],
        'password': data['password'],
        'port': data.get('port', 22),
        'enabled': data.get('enabled', True),
        'description': data.get('description', '')
    }

    try:
        success = cluster_manager.add_cluster(cluster_id, config)
        if success:
            # Immediately fetch cluster data after successful addition
            try:
                cluster_manager.update_cluster_data(cluster_id)
                print(f"Successfully added and initialized cluster: {data['name']} ({cluster_id})")
            except Exception as e:
                print(f"Cluster added but initial data fetch failed: {e}")

            return jsonify({
                'success': True,
                'cluster_id': cluster_id,
                'message': f"Cluster '{data['name']}' added successfully and data is being fetched"
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to add cluster'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/clusters/<cluster_id>', methods=['PUT'])
def api_update_cluster(cluster_id):
    """API endpoint to update cluster configuration"""
    data = request.get_json()

    config = {
        'name': data.get('name', ''),
        'host': data.get('host', ''),
        'username': data.get('username', ''),
        'password': data.get('password', ''),
        'port': data.get('port', 22),
        'enabled': data.get('enabled', True),
        'description': data.get('description', '')
    }

    try:
        success = cluster_manager.update_cluster(cluster_id, config)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Failed to update cluster'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/clusters/<cluster_id>', methods=['DELETE'])
def api_delete_cluster(cluster_id):
    """API endpoint to delete a cluster"""
    try:
        success = cluster_manager.remove_cluster(cluster_id)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Cluster not found'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/clusters/<cluster_id>/test', methods=['POST'])
def api_test_cluster(cluster_id):
    """API endpoint to test cluster connection"""
    try:
        result = cluster_manager.test_cluster_connection(cluster_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test-connection', methods=['POST'])
def api_test_connection():
    """API endpoint to test connection without saving cluster"""
    data = request.get_json()

    required_fields = ['host', 'username', 'password']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'success': False, 'error': f'Missing required field: {field}'})

    try:
        host = data['host']
        username = data['username']
        password = data['password']
        port = data.get('port', 22)

        # Step 1: Network connectivity test
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()

            if result != 0:
                return jsonify({
                    'success': False,
                    'error': f'Cannot connect to {host}:{port} - Host unreachable or port closed'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Network connectivity test failed: {str(e)}'
            })

        # Step 2: Create temporary SlurmManager for SSH and SLURM testing
        from slurm_manager import SlurmManager

        # Create a temporary manager with the test credentials
        test_manager = SlurmManager()
        test_manager.slurm_host = host
        test_manager.ssh_password = password
        test_manager.config.SLURM_USER = username
        test_manager.use_ssh = True

        # Step 3: Test SSH connection
        try:
            ssh_result = test_manager.run_command('echo "test"')
            if ssh_result != 'test':
                return jsonify({
                    'success': False,
                    'error': 'SSH authentication failed - please check username/password'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'SSH connection failed: {str(e)}'
            })

        # Step 4: Test SLURM commands
        try:
            slurm_version = test_manager.run_command('squeue --version')
            if slurm_version:
                return jsonify({
                    'success': True,
                    'message': 'Connection successful! SSH and SLURM are working.',
                    'slurm_version': slurm_version,
                    'details': f'Successfully connected to {host} as {username}'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'SSH works but SLURM commands are not available on this host'
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'SLURM test failed: {str(e)}'
            })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Test failed: {str(e)}'})

# DISABLED: SocketIO handlers removed
# @socketio.on('connect')
# @socketio.on('disconnect')

@app.route('/api/manual_refresh', methods=['POST'])
def api_manual_refresh():
    """API endpoint for manual refresh"""
    try:
        update_dashboard_data()
        return jsonify({'success': True, 'message': 'Data updated successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# DISABLED: SocketIO request handler removed
# @socketio.on('request_update')

if __name__ == '__main__':
    # DISABLED: Background updater and SocketIO
    print("Starting SLURM Dashboard (SocketIO disabled, on-demand data fetching)")
    print(f"Dashboard will be available at: http://localhost:{app.config['PORT']}")

    # Run regular Flask app
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
