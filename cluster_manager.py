"""
Multi-Cluster Manager for SLURM Dashboard
Handles multiple SLURM clusters with dynamic configuration
"""
import json
import os
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
from slurm_manager import SlurmManager

class ClusterManager:
    def __init__(self, config_file='clusters.json'):
        self.config_file = config_file
        self.clusters = {}
        self.cluster_data = {}
        self.data_lock = threading.Lock()
        self.load_clusters()

    def load_clusters(self):
        """Load cluster configurations from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    cluster_configs = json.load(f)

                for cluster_id, config in cluster_configs.items():
                    self.add_cluster_instance(cluster_id, config)

                print(f"Loaded {len(self.clusters)} clusters from configuration")
            except Exception as e:
                print(f"Error loading cluster configuration: {e}")
        else:
            # Create default cluster if no config exists
            self.create_default_cluster()

    def create_default_cluster(self):
        """Create default cluster from environment variables (only if configured)"""
        # Only create default cluster if SLURM_HOST is explicitly set
        slurm_host = os.environ.get('SLURM_HOST')

        if slurm_host and slurm_host not in ['localhost', '127.0.0.1', '']:
            default_config = {
                'name': os.environ.get('CLUSTER_NAME', 'Default Cluster'),
                'host': slurm_host,
                'username': os.environ.get('SLURM_USER', 'slurmadmin'),
                'password': os.environ.get('SLURM_SSH_PASSWORD', ''),
                'port': int(os.environ.get('SLURM_SSH_PORT', '22')),
                'enabled': True,
                'description': 'Default cluster from environment'
            }

            self.add_cluster('default', default_config)
            print("Created default cluster from environment variables")
        else:
            print("No default cluster configured - start by adding clusters via web interface")

    def save_clusters(self):
        """Save cluster configurations to file"""
        try:
            cluster_configs = {}
            for cluster_id, manager in self.clusters.items():
                cluster_configs[cluster_id] = {
                    'name': manager.cluster_name,
                    'host': manager.slurm_host,
                    'username': manager.config.SLURM_USER,
                    'password': manager.ssh_password,
                    'port': getattr(manager, 'ssh_port', 22),
                    'enabled': getattr(manager, 'enabled', True),
                    'description': getattr(manager, 'description', '')
                }

            with open(self.config_file, 'w') as f:
                json.dump(cluster_configs, f, indent=2)

            print(f"Saved {len(cluster_configs)} clusters to configuration")
        except Exception as e:
            print(f"Error saving cluster configuration: {e}")

    def add_cluster_instance(self, cluster_id: str, config: dict):
        """Add a cluster instance with configuration"""
        try:
            # Create a custom SlurmManager for this cluster
            manager = SlurmManager()

            # Set cluster-specific configuration BEFORE determining SSH usage
            manager.cluster_name = config.get('name', cluster_id)
            manager.slurm_host = config.get('host', 'localhost')
            manager.ssh_password = config.get('password', '')
            manager.ssh_port = config.get('port', 22)
            manager.enabled = config.get('enabled', True)
            manager.description = config.get('description', '')

            # Set username in config object
            manager.config.SLURM_USER = config.get('username', 'slurmadmin')

            # Determine SSH usage and validate credentials
            manager.use_ssh = manager.slurm_host not in ['localhost', '127.0.0.1']

            if manager.use_ssh:
                print(f"Cluster {cluster_id}: Using SSH to {manager.slurm_host} as {manager.config.SLURM_USER}")
                if not manager.ssh_password:
                    print(f"Warning: No SSH password provided for cluster {cluster_id}")
            else:
                print(f"Cluster {cluster_id}: Using local SLURM commands")

            self.clusters[cluster_id] = manager

            # Initialize data structure
            with self.data_lock:
                self.cluster_data[cluster_id] = {
                    'queue_jobs': [],
                    'running_jobs': [],
                    'cluster_info': {},
                    'last_update': None,
                    'status': 'unknown',
                    'error': None
                }

            return True
        except Exception as e:
            print(f"Error adding cluster {cluster_id}: {e}")
            return False

    def add_cluster(self, cluster_id: str, config: dict) -> bool:
        """Add a new cluster and save configuration"""
        if self.add_cluster_instance(cluster_id, config):
            self.save_clusters()
            return True
        return False

    def remove_cluster(self, cluster_id: str) -> bool:
        """Remove a cluster"""
        if cluster_id in self.clusters:
            del self.clusters[cluster_id]

            with self.data_lock:
                if cluster_id in self.cluster_data:
                    del self.cluster_data[cluster_id]

            self.save_clusters()
            return True
        return False

    def update_cluster(self, cluster_id: str, config: dict) -> bool:
        """Update cluster configuration"""
        if cluster_id in self.clusters:
            self.remove_cluster(cluster_id)
            return self.add_cluster(cluster_id, config)
        return False

    def test_cluster_connection(self, cluster_id: str) -> dict:
        """Test connection to a specific cluster"""
        if cluster_id not in self.clusters:
            return {'success': False, 'error': 'Cluster not found'}

        manager = self.clusters[cluster_id]

        try:
            # Test basic connection with a simple command
            result = manager.run_command('echo "test"')
            if result == 'test':
                # Test SLURM commands
                version_result = manager.run_command('squeue --version')
                if version_result:
                    return {
                        'success': True,
                        'message': 'Connection successful',
                        'slurm_version': version_result
                    }
                else:
                    return {
                        'success': False,
                        'error': 'SLURM commands not available'
                    }
            else:
                return {'success': False, 'error': 'SSH connection failed'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_cluster_data(self, cluster_id: str):
        """Update data for a specific cluster"""
        if cluster_id not in self.clusters:
            return

        manager = self.clusters[cluster_id]

        if not getattr(manager, 'enabled', True):
            return

        try:
            queue_jobs = manager.get_queue_jobs()
            running_jobs = manager.get_running_jobs()
            cluster_info = manager.get_cluster_info()

            with self.data_lock:
                self.cluster_data[cluster_id].update({
                    'queue_jobs': queue_jobs,
                    'running_jobs': running_jobs,
                    'cluster_info': cluster_info,
                    'last_update': time.time(),
                    'status': 'online',
                    'error': None
                })

        except Exception as e:
            print(f"Error updating data for cluster {cluster_id}: {e}")
            with self.data_lock:
                self.cluster_data[cluster_id].update({
                    'status': 'error',
                    'error': str(e),
                    'last_update': time.time()
                })

    def update_all_clusters(self):
        """Update data for all enabled clusters"""
        threads = []

        for cluster_id in self.clusters:
            if getattr(self.clusters[cluster_id], 'enabled', True):
                thread = threading.Thread(
                    target=self.update_cluster_data,
                    args=(cluster_id,)
                )
                thread.start()
                threads.append(thread)

        # Wait for all threads to complete (with shorter timeout)
        for thread in threads:
            thread.join(timeout=10)  # Reduced timeout for faster response

    def get_cluster_data(self, cluster_id: str) -> dict:
        """Get data for a specific cluster"""
        with self.data_lock:
            return self.cluster_data.get(cluster_id, {})

    def get_all_cluster_data(self) -> dict:
        """Get data for all clusters"""
        with self.data_lock:
            return dict(self.cluster_data)

    def get_cluster_list(self) -> List[dict]:
        """Get list of all clusters with basic info (no data fetching)"""
        clusters = []

        for cluster_id, manager in self.clusters.items():
            # Get cached data without blocking
            try:
                data = self.cluster_data.get(cluster_id, {})
            except:
                data = {}

            clusters.append({
                'id': cluster_id,
                'name': getattr(manager, 'cluster_name', cluster_id),
                'host': manager.slurm_host,
                'username': manager.config.SLURM_USER,
                'enabled': getattr(manager, 'enabled', True),
                'status': data.get('status', 'offline'),
                'last_update': data.get('last_update', 'never'),
                'description': getattr(manager, 'description', ''),
                'queue_count': len(data.get('queue_jobs', [])),
                'running_count': len(data.get('running_jobs', []))
            })

        return clusters

    def change_job_priority(self, cluster_id: str, job_id: str, new_priority: int) -> bool:
        """Change job priority on a specific cluster"""
        if cluster_id not in self.clusters:
            return False

        manager = self.clusters[cluster_id]
        return manager.change_job_priority(job_id, new_priority)

    def get_job_details(self, cluster_id: str, job_id: str) -> dict:
        """Get job details from a specific cluster"""
        if cluster_id not in self.clusters:
            return None

        manager = self.clusters[cluster_id]
        return manager.get_job_details(job_id)

    def requeue_job(self, cluster_id: str, job_id: str) -> tuple:
        """Requeue a running job on a specific cluster"""
        if cluster_id not in self.clusters:
            return False, "Cluster not found"

        manager = self.clusters[cluster_id]
        return manager.requeue_job(job_id)

    def cancel_job(self, cluster_id: str, job_id: str) -> tuple:
        """Cancel a running job on a specific cluster"""
        if cluster_id not in self.clusters:
            return False, "Cluster not found"

        manager = self.clusters[cluster_id]
        return manager.cancel_job(job_id)
