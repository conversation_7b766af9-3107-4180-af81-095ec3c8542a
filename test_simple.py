#!/usr/bin/env python3
"""
Test the simple dashboard
"""
import requests
import json

def test_simple_dashboard():
    """Test the simple dashboard"""
    
    base_url = "http://localhost:5001"
    
    print("Testing Simple Dashboard...")
    print("=" * 50)
    
    # Test 1: Get clusters
    print("1. Testing /api/clusters...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            clusters = response.json()
            print(f"   ✅ Found {len(clusters)} clusters")
            for cluster in clusters:
                print(f"     - {cluster['id']}: {cluster['name']} ({cluster['host']})")
                cluster_id = cluster['id']
        else:
            print(f"   ❌ Error: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    if not clusters:
        print("   ℹ️  No clusters found - this is expected for a fresh start")
        return
    
    # Test 2: Get cluster data
    cluster_id = clusters[0]['id']
    print(f"\n2. Testing cluster data for {cluster_id}...")
    try:
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/data", timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Status: {data.get('status', 'unknown')}")
            print(f"   ✅ Queue jobs: {len(data.get('queue_jobs', []))}")
            print(f"   ✅ Running jobs: {len(data.get('running_jobs', []))}")
            
            # Show some running jobs
            running_jobs = data.get('running_jobs', [])
            if running_jobs:
                print(f"   📋 First 3 running jobs:")
                for job in running_jobs[:3]:
                    print(f"     - Job {job['job_id']}: {job['name']} on {job['nodes']}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_simple_dashboard()
