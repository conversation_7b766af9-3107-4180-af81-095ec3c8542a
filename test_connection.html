<!DOCTYPE html>
<html>
<head>
    <title>Test SLURM Connection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:disabled { background: #ccc; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test SLURM Connection</h1>
    <p>Quick test page to verify connection testing functionality</p>
    
    <form id="testForm">
        <div class="form-group">
            <label>SLURM Host:</label>
            <input type="text" id="host" placeholder="slurm-head.company.com" required>
        </div>
        
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="username" placeholder="slurmadmin" required>
        </div>
        
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="password" required>
        </div>
        
        <div class="form-group">
            <label>SSH Port:</label>
            <input type="number" id="port" value="22" min="1" max="65535">
        </div>
        
        <button type="submit" id="testBtn">Test Connection</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const testBtn = document.getElementById('testBtn');
            const result = document.getElementById('result');
            
            const data = {
                host: document.getElementById('host').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                port: parseInt(document.getElementById('port').value) || 22
            };
            
            testBtn.textContent = 'Testing...';
            testBtn.disabled = true;
            result.innerHTML = '';
            
            try {
                const response = await fetch('/api/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const resultData = await response.json();
                
                if (resultData.success) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <strong>✅ Connection Successful!</strong><br>
                        ${resultData.message}<br>
                        ${resultData.slurm_version ? `SLURM Version: ${resultData.slurm_version}` : ''}
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `<strong>❌ Connection Failed:</strong><br>${resultData.error}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<strong>❌ Error:</strong><br>${error.message}`;
            } finally {
                testBtn.textContent = 'Test Connection';
                testBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
