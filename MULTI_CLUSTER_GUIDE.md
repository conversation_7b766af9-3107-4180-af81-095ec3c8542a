# Multi-Cluster SLURM Dashboard Guide

This guide explains how to use the multi-cluster functionality of the SLURM Dashboard, allowing you to manage multiple SLURM clusters from a single interface.

## 🌟 Features

### Multi-Cluster Management
- **Add/Edit/Delete Clusters**: Dynamic cluster configuration
- **Password Authentication**: Secure SSH password-based access
- **Real-time Monitoring**: Live updates from all configured clusters
- **Cluster Switching**: Easy switching between different clusters
- **Connection Testing**: Test cluster connectivity before saving

### Enhanced Dashboard
- **Cluster Selector**: Dropdown to select active cluster
- **Cluster Status**: Visual indicators for cluster health
- **Unified Interface**: Same powerful features for all clusters
- **Drag-and-Drop Priority**: Job priority management per cluster

## 🚀 Getting Started

### 1. Initial Setup

The dashboard automatically creates a default cluster from environment variables:

```bash
# Environment variables for default cluster
SLURM_HOST=your-slurm-head-node.example.com
SLURM_USER=slurmadmin
SLURM_SSH_PASSWORD=your-password
CLUSTER_NAME=Production Cluster
```

### 2. Adding New Clusters

#### Via Web Interface
1. Click the **"Add Cluster"** button in the header
2. Fill in the cluster details:
   - **Cluster Name**: Display name for the cluster
   - **Host/IP Address**: SLURM head node hostname or IP
   - **Username**: SSH username with SLURM access
   - **Password**: SSH password for authentication
   - **SSH Port**: SSH port (default: 22)
   - **Description**: Optional cluster description
   - **Enable**: Whether to monitor this cluster

3. Click **"Test Connection"** to verify connectivity
4. Click **"Save Cluster"** to add the cluster

#### Via Configuration File
Create or edit `clusters.json`:

```json
{
  "prod": {
    "name": "Production Cluster",
    "host": "slurm-prod.company.com",
    "username": "slurmadmin",
    "password": "secure-password",
    "port": 22,
    "enabled": true,
    "description": "Main production environment"
  },
  "dev": {
    "name": "Development Cluster", 
    "host": "slurm-dev.company.com",
    "username": "slurmdev",
    "password": "dev-password",
    "port": 2222,
    "enabled": true,
    "description": "Development and testing"
  }
}
```

### 3. Using the Dashboard

#### Cluster Selection
- Use the **cluster dropdown** in the header to select active cluster
- The dashboard will automatically load data for the selected cluster
- Queue jobs and running jobs will update to show the selected cluster's data

#### Cluster Management Panel
- Click the **dashboard title** to open the cluster management panel
- View all configured clusters with their status
- Edit, delete, or view individual clusters
- See real-time statistics for each cluster

## 🔧 API Endpoints

### Cluster Management
```bash
# Get all clusters
GET /api/clusters

# Add new cluster
POST /api/clusters
{
  "name": "New Cluster",
  "host": "slurm.example.com",
  "username": "admin",
  "password": "password",
  "port": 22,
  "enabled": true,
  "description": "Description"
}

# Update cluster
PUT /api/clusters/{cluster_id}
{
  "name": "Updated Name",
  ...
}

# Delete cluster
DELETE /api/clusters/{cluster_id}

# Test cluster connection
POST /api/clusters/{cluster_id}/test
```

### Cluster Data
```bash
# Get cluster queue jobs
GET /api/cluster/{cluster_id}/queue_jobs

# Get cluster running jobs
GET /api/cluster/{cluster_id}/running_jobs

# Get cluster information
GET /api/cluster/{cluster_id}/info

# Get all cluster data
GET /api/cluster/{cluster_id}/data

# Update job priority
POST /api/cluster/{cluster_id}/update_priority
{
  "job_id": "12345",
  "priority": 1000
}

# Get job details
GET /api/cluster/{cluster_id}/job_details/{job_id}
```

## 🔒 Security Considerations

### Password Storage
- Passwords are stored in `clusters.json` file
- **Secure the file**: `chmod 600 clusters.json`
- **Use strong passwords**: Complex, unique passwords for each cluster
- **Regular rotation**: Change passwords periodically

### Network Security
- **SSH Configuration**: Ensure SSH is properly configured on SLURM hosts
- **Firewall Rules**: Restrict SSH access to dashboard host
- **VPN Access**: Consider VPN for remote dashboard access

### Access Control
- **Dashboard Authentication**: Consider adding authentication to the dashboard
- **SLURM Permissions**: Use dedicated users with minimal required permissions
- **Audit Logging**: Monitor SSH access and SLURM command execution

## 📊 Monitoring Multiple Clusters

### Dashboard Features
- **Real-time Updates**: All clusters update automatically
- **Status Indicators**: Visual cluster health status
- **Unified View**: Switch between clusters seamlessly
- **Aggregate Statistics**: View total jobs across all clusters

### Cluster Status
- **Online**: Cluster is accessible and responding
- **Offline**: Cannot connect to cluster
- **Error**: Connection established but SLURM commands failing

### Performance Considerations
- **Parallel Updates**: Clusters update in parallel for better performance
- **Timeout Handling**: Configurable timeouts for unresponsive clusters
- **Error Recovery**: Automatic retry for failed connections

## 🛠️ Configuration Options

### Environment Variables
```bash
# Default cluster settings
SLURM_HOST=default-cluster.com
SLURM_USER=slurmadmin
SLURM_SSH_PASSWORD=default-password
CLUSTER_NAME=Default Cluster

# Dashboard settings
REFRESH_INTERVAL=5
MAX_QUEUE_JOBS=100
MAX_RUNNING_JOBS=50
```

### Cluster Configuration
```json
{
  "cluster_id": {
    "name": "Display Name",
    "host": "hostname or IP",
    "username": "SSH username",
    "password": "SSH password",
    "port": 22,
    "enabled": true,
    "description": "Optional description"
  }
}
```

## 🔄 Data Flow

### Update Process
1. **Background Thread**: Continuously updates all enabled clusters
2. **Parallel Execution**: Each cluster updates independently
3. **WebSocket Push**: Real-time updates to connected clients
4. **Error Handling**: Failed clusters marked as offline

### Priority Management
1. **Cluster Selection**: User selects target cluster
2. **Drag and Drop**: User reorders jobs in queue
3. **API Call**: Priority update sent to specific cluster
4. **SLURM Command**: `scontrol update` executed on target cluster
5. **Immediate Refresh**: Cluster data refreshed to show changes

## 🐛 Troubleshooting

### Common Issues

#### Cluster Not Connecting
```bash
# Check SSH connectivity
ssh username@cluster-host

# Verify SLURM commands
ssh username@cluster-host 'squeue --version'

# Check password authentication
# Edit /etc/ssh/sshd_config on cluster host:
# PasswordAuthentication yes
```

#### Jobs Not Updating
```bash
# Check cluster status in management panel
# Verify SLURM user permissions
# Review dashboard logs for errors
```

#### Priority Changes Failing
```bash
# Verify sudo permissions on cluster
# Check SLURM job ownership
# Ensure user has priority change permissions
```

### Debug Mode
Enable debug logging:
```bash
export FLASK_ENV=development
export LOG_LEVEL=DEBUG
```

### Log Analysis
Check application logs for:
- SSH connection errors
- SLURM command failures
- Authentication issues
- Network timeouts

## 📈 Best Practices

### Cluster Organization
- **Naming Convention**: Use consistent, descriptive names
- **Environment Separation**: Separate prod/dev/test clusters
- **Documentation**: Maintain cluster descriptions and contact info

### Security
- **Least Privilege**: Use accounts with minimal required permissions
- **Password Management**: Use password managers for secure storage
- **Regular Audits**: Review cluster access and permissions

### Performance
- **Selective Monitoring**: Disable unused clusters
- **Appropriate Intervals**: Adjust refresh rates based on cluster size
- **Resource Limits**: Set reasonable job display limits

### Maintenance
- **Regular Updates**: Keep dashboard and dependencies updated
- **Backup Configuration**: Backup `clusters.json` regularly
- **Monitor Resources**: Watch dashboard resource usage

## 🎯 Advanced Usage

### Custom Scripts
Integrate with existing tools:
```bash
# Export cluster data
curl http://dashboard:5000/api/clusters > clusters_backup.json

# Bulk cluster operations
for cluster in $(curl -s http://dashboard:5000/api/clusters | jq -r '.[].id'); do
  echo "Cluster: $cluster"
  curl -s "http://dashboard:5000/api/cluster/$cluster/info"
done
```

### Automation
- **Cluster Discovery**: Automatically discover new clusters
- **Health Monitoring**: Alert on cluster failures
- **Capacity Planning**: Track resource usage trends

This multi-cluster functionality transforms the SLURM dashboard into a comprehensive cluster management platform, providing unified monitoring and control across your entire HPC infrastructure.
