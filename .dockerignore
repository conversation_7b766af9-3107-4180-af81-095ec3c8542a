# Docker ignore file for SLURM Dashboard

# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
ENV/
env/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/*
*.log

# SSH keys (will be mounted)
ssh/id_rsa*

# Environment files
.env
.env.local
.env.*.local

# Documentation
README.md
*.md

# Debug and test files (if any remain)
debug_*.py
test_*.py
simple_*.py
trigger_*.py
verify_*.py

# Cluster configuration with credentials
clusters.json
