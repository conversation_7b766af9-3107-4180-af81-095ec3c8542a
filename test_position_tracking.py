#!/usr/bin/env python3
"""
Test the improved position tracking for drag-and-drop
"""
import requests

def test_position_tracking():
    """Test the position tracking mechanism"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Improved Position Tracking...")
    print("=" * 50)
    
    # Get current queue
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        print(f"✅ Using cluster: {clusters[0]['name']} ({cluster_id})")
        
        # Get current queue
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code != 200:
            print(f"❌ Failed to get queue jobs: {response.status_code}")
            return
            
        queue_jobs = response.json()
        print(f"✅ Found {len(queue_jobs)} queue jobs")
        
        if len(queue_jobs) < 3:
            print("ℹ️  Need at least 3 jobs in queue to test position tracking")
            return
            
        print("\n📋 Current queue order (by priority):")
        for i, job in enumerate(queue_jobs):
            short_id = job['job_id'].split('_')[0]
            print(f"   Position {i+1}: Job {short_id} (Full: {job['job_id']}) - Priority: {job.get('priority', 'N/A')}")
        
        # Test scenarios
        print(f"\n🎯 POSITION TRACKING TEST SCENARIOS:")
        
        # Scenario 1: Move last job to top
        if len(queue_jobs) >= 3:
            last_job = queue_jobs[-1]['job_id']
            last_job_short = last_job.split('_')[0]
            
            print(f"\n📝 Scenario 1: Move job {last_job_short} from position {len(queue_jobs)} to position 1")
            print(f"   Original order: {[job['job_id'].split('_')[0] for job in queue_jobs]}")
            
            # Simulate the new order after drag
            new_order = [last_job] + [job['job_id'] for job in queue_jobs[:-1]]
            new_order_short = [job_id.split('_')[0] for job_id in new_order]
            
            print(f"   New order after drag: {new_order_short}")
            print(f"   Reverse order for scontrol top: {new_order_short[::-1]}")
            
            print(f"   Expected scontrol commands:")
            for i, job_id in enumerate(reversed(new_order)):
                step = i + 1
                base_id = job_id.split('_')[0]
                print(f"     Step {step}: scontrol top {base_id}")
            
            print(f"   Expected final SLURM order: {new_order_short}")
        
        # Scenario 2: Move middle job to position 2
        if len(queue_jobs) >= 4:
            middle_job = queue_jobs[2]['job_id']  # 3rd job (index 2)
            middle_job_short = middle_job.split('_')[0]
            
            print(f"\n📝 Scenario 2: Move job {middle_job_short} from position 3 to position 2")
            
            # Simulate moving job from position 3 to position 2
            original_order = [job['job_id'] for job in queue_jobs]
            new_order = original_order.copy()
            
            # Remove job from position 2 (3rd position) and insert at position 1 (2nd position)
            moved_job = new_order.pop(2)
            new_order.insert(1, moved_job)
            
            original_short = [job_id.split('_')[0] for job_id in original_order]
            new_order_short = [job_id.split('_')[0] for job_id in new_order]
            
            print(f"   Original order: {original_short}")
            print(f"   New order after drag: {new_order_short}")
            print(f"   Reverse order for scontrol top: {new_order_short[::-1]}")
            
            print(f"   Expected scontrol commands:")
            for i, job_id in enumerate(reversed(new_order)):
                step = i + 1
                base_id = job_id.split('_')[0]
                print(f"     Step {step}: scontrol top {base_id}")
            
            print(f"   Expected final SLURM order: {new_order_short}")
        
        print(f"\n🔧 DEBUGGING FEATURES ADDED:")
        print(f"   ✅ Enhanced console logging with emojis")
        print(f"   ✅ Visual order tracking after drag operations")
        print(f"   ✅ Detailed change descriptions")
        print(f"   ✅ Step-by-step scontrol command logging")
        print(f"   ✅ Success/failure tracking for each command")
        print(f"   ✅ Longer delays (800ms) between commands")
        
        print(f"\n🎮 HOW TO TEST:")
        print(f"   1. Open: http://localhost:5000")
        print(f"   2. Open browser console (F12 → Console)")
        print(f"   3. Drag the LAST job to the TOP position")
        print(f"   4. Watch console for detailed logging:")
        print(f"      - 🖱️ DRAG COMPLETED messages")
        print(f"      - 📋 ORIGINAL ORDER vs CURRENT VISUAL ORDER")
        print(f"      - 🔍 VERIFICATION of new position")
        print(f"   5. Click 'Save New Order' and watch:")
        print(f"      - 🎯 APPLYING QUEUE ORDER")
        print(f"      - 🔄 Step-by-step scontrol commands")
        print(f"      - ✅ Success confirmations")
        
        print(f"\n⚠️  WHAT TO LOOK FOR:")
        print(f"   • Job should visually stay in new position after drag")
        print(f"   • Console should show correct visual order capture")
        print(f"   • Save Order button should appear with correct description")
        print(f"   • scontrol commands should execute in reverse order")
        print(f"   • Final queue should match the dragged arrangement")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_position_tracking()
