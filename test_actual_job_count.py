#!/usr/bin/env python3
"""
Test the actual job count using direct SLURM commands
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import <PERSON>luster<PERSON>ana<PERSON>

def test_actual_job_count():
    """Test the actual job count with different SLURM commands"""
    
    print("Testing Actual Job Count with Direct SLURM Commands...")
    print("=" * 60)
    
    # Initialize cluster manager
    cluster_manager = ClusterManager()
    
    if not cluster_manager.clusters:
        print("❌ No clusters found")
        return
    
    # Get the first cluster
    cluster_id = list(cluster_manager.clusters.keys())[0]
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"✅ Testing cluster: {manager.cluster_name} ({manager.slurm_host})")
    print(f"   User: {manager.config.SLURM_USER}")
    print(f"   SSH: {manager.use_ssh}")
    
    # Test 1: Basic squeue command (what you're using)
    print(f"\n1. Testing basic squeue command...")
    try:
        basic_command = "squeue"
        output = manager.run_command(basic_command)
        if output:
            lines = output.strip().split('\n')
            # Remove header line
            job_lines = [line for line in lines[1:] if line.strip()]
            print(f"   ✅ Total jobs (squeue): {len(job_lines)}")
            
            # Count by state
            running_count = 0
            pending_count = 0
            other_count = 0
            
            for line in job_lines:
                parts = line.split()
                if len(parts) >= 5:  # JOBID PARTITION NAME USER ST
                    state = parts[4]  # ST column
                    if state == 'R':
                        running_count += 1
                    elif state == 'PD':
                        pending_count += 1
                    else:
                        other_count += 1
            
            print(f"   📊 Running (R): {running_count}")
            print(f"   📊 Pending (PD): {pending_count}")
            print(f"   📊 Other states: {other_count}")
        else:
            print(f"   ❌ Basic squeue failed")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Running jobs only (current dashboard command)
    print(f"\n2. Testing running jobs command (current dashboard)...")
    try:
        running_command = "squeue -t R -o '%i|%j|%u|%T|%P|%M|%N' --noheader"
        output = manager.run_command(running_command)
        if output:
            lines = [line for line in output.strip().split('\n') if line.strip()]
            print(f"   ✅ Running jobs (squeue -t R): {len(lines)}")
            
            # Show first few jobs
            print(f"   📋 First 5 running jobs:")
            for i, line in enumerate(lines[:5]):
                parts = line.split('|')
                if len(parts) >= 3:
                    print(f"     {i+1}. Job {parts[0]}: {parts[1]} (User: {parts[2]})")
        else:
            print(f"   ❌ Running jobs command failed")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Pending jobs only
    print(f"\n3. Testing pending jobs command...")
    try:
        pending_command = "squeue -t PD -o '%i|%j|%u|%T|%P|%Q|%r|%S' --noheader"
        output = manager.run_command(pending_command)
        if output:
            lines = [line for line in output.strip().split('\n') if line.strip()]
            print(f"   ✅ Pending jobs (squeue -t PD): {len(lines)}")
        else:
            print(f"   ✅ No pending jobs or command failed")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: All states
    print(f"\n4. Testing all job states...")
    try:
        all_command = "squeue -o '%i|%j|%u|%T|%P' --noheader"
        output = manager.run_command(all_command)
        if output:
            lines = [line for line in output.strip().split('\n') if line.strip()]
            print(f"   ✅ All jobs (squeue): {len(lines)}")
            
            # Count by state
            states = {}
            for line in lines:
                parts = line.split('|')
                if len(parts) >= 4:
                    state = parts[3]  # State column
                    states[state] = states.get(state, 0) + 1
            
            print(f"   📊 Job states breakdown:")
            for state, count in sorted(states.items()):
                print(f"     {state}: {count}")
        else:
            print(f"   ❌ All jobs command failed")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 5: Check current config limits
    print(f"\n5. Current configuration limits...")
    print(f"   MAX_RUNNING_JOBS: {manager.config.MAX_RUNNING_JOBS}")
    print(f"   MAX_QUEUE_JOBS: {manager.config.MAX_QUEUE_JOBS}")
    
    # Test 6: Test the actual dashboard methods
    print(f"\n6. Testing dashboard methods...")
    try:
        running_jobs = manager.get_running_jobs()
        queue_jobs = manager.get_queue_jobs()
        
        print(f"   ✅ Dashboard running jobs: {len(running_jobs)}")
        print(f"   ✅ Dashboard queue jobs: {len(queue_jobs)}")
        
        if len(running_jobs) == manager.config.MAX_RUNNING_JOBS:
            print(f"   ⚠️  Running jobs hit the limit! Increase MAX_RUNNING_JOBS")
        
        if len(queue_jobs) == manager.config.MAX_QUEUE_JOBS:
            print(f"   ⚠️  Queue jobs hit the limit! Increase MAX_QUEUE_JOBS")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_actual_job_count()
