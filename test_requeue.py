#!/usr/bin/env python3
"""
Test script for SLURM requeue functionality
Tests all operations with job ID 902394
"""

from slurm_manager import SlurmManager

def main():
    print("🔧 SLURM Requeue Function Test")
    print("=" * 50)
    
    # Initialize SLURM manager
    slurm = SlurmManager()
    
    # Test all operations with job 902394
    slurm.test_job_operations("902394")
    
    print("\n" + "=" * 50)
    print("✅ Test completed. Check output above for any issues.")
    print("\nTo run actual requeue (if tests pass):")
    print("   slurm.requeue_job('902394')")

if __name__ == "__main__":
    main()
