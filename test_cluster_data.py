#!/usr/bin/env python3
"""
Test cluster data fetching directly
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import ClusterManager

def test_cluster_data():
    """Test fetching data from the cluster"""
    
    print("Testing Cluster Data Fetching...")
    print("=" * 50)
    
    # Create cluster manager
    cluster_manager = ClusterManager()
    
    print(f"Found {len(cluster_manager.clusters)} clusters:")
    for cluster_id, manager in cluster_manager.clusters.items():
        print(f"  - {cluster_id}: {manager.cluster_name} ({manager.slurm_host})")
    
    if not cluster_manager.clusters:
        print("❌ No clusters found!")
        return
    
    # Get the first cluster
    cluster_id = list(cluster_manager.clusters.keys())[0]
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"\nTesting cluster: {cluster_id}")
    print(f"Name: {manager.cluster_name}")
    print(f"Host: {manager.slurm_host}")
    print(f"User: {manager.config.SLURM_USER}")
    print(f"SSH: {manager.use_ssh}")
    print(f"Password set: {'Yes' if manager.ssh_password else 'No'}")
    
    print("\n1. Testing basic SSH connection...")
    try:
        result = manager.run_command('echo "test"')
        if result == 'test':
            print("   ✅ SSH connection works!")
        else:
            print(f"   ❌ SSH failed. Result: {result}")
            return
    except Exception as e:
        print(f"   ❌ SSH error: {e}")
        return
    
    print("\n2. Testing SLURM queue jobs...")
    try:
        queue_jobs = manager.get_queue_jobs()
        print(f"   Found {len(queue_jobs)} queue jobs")
        for job in queue_jobs[:3]:  # Show first 3
            print(f"     - Job {job['job_id']}: {job['name']} (Priority: {job['priority']})")
    except Exception as e:
        print(f"   ❌ Queue jobs error: {e}")
    
    print("\n3. Testing SLURM running jobs...")
    try:
        running_jobs = manager.get_running_jobs()
        print(f"   Found {len(running_jobs)} running jobs")
        for job in running_jobs[:3]:  # Show first 3
            print(f"     - Job {job['job_id']}: {job['name']} on {job['nodes']}")
    except Exception as e:
        print(f"   ❌ Running jobs error: {e}")
    
    print("\n4. Testing cluster info...")
    try:
        cluster_info = manager.get_cluster_info()
        print(f"   Cluster: {cluster_info.get('name', 'Unknown')}")
        nodes = cluster_info.get('nodes', {})
        print(f"   Nodes: {nodes.get('total', 0)} total, {nodes.get('idle', 0)} idle")
        print(f"   CPUs: {cluster_info.get('total_cpus', 0)}")
    except Exception as e:
        print(f"   ❌ Cluster info error: {e}")
    
    print("\n5. Testing cluster manager data update...")
    try:
        cluster_manager.update_cluster_data(cluster_id)
        data = cluster_manager.get_cluster_data(cluster_id)
        print(f"   Status: {data.get('status', 'unknown')}")
        print(f"   Queue jobs: {len(data.get('queue_jobs', []))}")
        print(f"   Running jobs: {len(data.get('running_jobs', []))}")
        print(f"   Last update: {data.get('last_update', 'never')}")
        if data.get('error'):
            print(f"   Error: {data['error']}")
    except Exception as e:
        print(f"   ❌ Data update error: {e}")

if __name__ == "__main__":
    test_cluster_data()
