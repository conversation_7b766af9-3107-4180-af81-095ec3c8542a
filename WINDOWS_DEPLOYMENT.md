# SLURM Dashboard - Windows Deployment Guide

This guide explains how to deploy the SLURM Dashboard on Windows systems with multi-cluster support and password-based SSH authentication.

## 🖥️ Windows Requirements

### System Requirements
- **Windows 10** (version 1809 or later) or **Windows 11**
- **Python 3.7+** installed from [python.org](https://python.org)
- **OpenSSH Client** (built into Windows 10+)
- **PowerShell 5.1+** or **PowerShell Core**

### Optional Requirements
- **Docker Desktop** (for containerized deployment)
- **Git for Windows** (for version control)

## 🚀 Quick Start (Windows)

### 1. Enable OpenSSH Client

OpenSSH Client is required for SSH connections to SLURM clusters:

```powershell
# Check if OpenSSH Client is installed
Get-WindowsCapability -Online | Where-Object Name -like 'OpenSSH.Client*'

# Install if not present
Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******
```

**Alternative via Settings:**
1. Go to **Settings** > **Apps** > **Optional Features**
2. Click **"Add a feature"**
3. Find and install **"OpenSSH Client"**

### 2. Run Windows Setup Script

```powershell
# Run the Windows setup script
.\setup_windows.ps1
```

### 3. Configure Environment

Edit the `.env` file created by the setup script:

```bash
# SLURM Connection Settings
SLURM_HOST=your-slurm-head-node.example.com
SLURM_USER=slurmadmin
SLURM_SSH_PASSWORD=your-ssh-password
CLUSTER_NAME=Production HPC Cluster

# Dashboard Settings
REFRESH_INTERVAL=5
FLASK_ENV=development
SECRET_KEY=your-secure-secret-key
```

### 4. Test Setup

```powershell
# Test the configuration
python test_setup.py
```

### 5. Start Dashboard

```powershell
# Start the dashboard
python main.py
```

### 6. Access Dashboard

Open your web browser and navigate to:
- Local: `http://localhost:5000`
- Network: `http://your-computer-ip:5000`

## 🔧 Windows-Specific Configuration

### SSH Authentication on Windows

The dashboard uses Windows built-in SSH client with the following approach:

1. **Password Authentication**: Automatically sends password when prompted
2. **Host Key Verification**: Disabled for convenience (can be enabled for security)
3. **Connection Timeout**: 10 seconds default timeout
4. **Error Handling**: Graceful fallback and error reporting

### PowerShell Execution Policy

If you encounter execution policy errors:

```powershell
# Check current policy
Get-ExecutionPolicy

# Set policy to allow local scripts (run as Administrator)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Firewall Configuration

Allow Python through Windows Firewall:

```powershell
# Allow Python through firewall (run as Administrator)
New-NetFirewallRule -DisplayName "Python Dashboard" -Direction Inbound -Program "C:\Python39\python.exe" -Action Allow
```

## 🐳 Docker Deployment on Windows

### Prerequisites
- **Docker Desktop for Windows**
- **WSL 2** (recommended backend)

### Setup Steps

1. **Install Docker Desktop**
   - Download from [docker.com](https://docker.com)
   - Enable WSL 2 integration

2. **Run Docker Setup**
   ```powershell
   # Use Docker deployment
   docker-compose up --build -d
   ```

3. **Access Dashboard**
   - Open `http://localhost:5000`

## 🔒 Windows Security Considerations

### SSH Security
- **Password Storage**: Passwords stored in `.env` file
- **File Permissions**: Secure the `.env` file appropriately
- **Network Security**: Use Windows Firewall to restrict access

### User Account Control (UAC)
- Dashboard runs as regular user (no admin privileges required)
- SSH connections use standard user permissions
- No system-level changes required

### Windows Defender
- Add Python and dashboard directory to exclusions if needed
- Monitor for false positives with SSH connections

## 🛠️ Troubleshooting Windows Issues

### Common Problems

#### 1. SSH Client Not Found
```
Error: SSH client not found
```

**Solution:**
```powershell
# Install OpenSSH Client
Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******

# Verify installation
ssh -V
```

#### 2. Python Not Found
```
Error: Python not found
```

**Solution:**
- Install Python from [python.org](https://python.org)
- Ensure "Add Python to PATH" is checked during installation
- Restart PowerShell after installation

#### 3. Permission Denied
```
Error: Permission denied (publickey,password)
```

**Solution:**
- Verify SSH password is correct
- Check if password authentication is enabled on SLURM host
- Test manual SSH connection: `ssh username@slurm-host`

#### 4. Connection Timeout
```
Error: SSH connection timed out
```

**Solution:**
- Check network connectivity to SLURM host
- Verify firewall settings
- Increase timeout in configuration

#### 5. PowerShell Execution Policy
```
Error: Execution of scripts is disabled
```

**Solution:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Debug Mode

Enable debug logging:

```powershell
# Set environment variables for debugging
$env:FLASK_ENV = "development"
$env:LOG_LEVEL = "DEBUG"

# Run dashboard
python main.py
```

### Network Diagnostics

Test network connectivity:

```powershell
# Test basic connectivity
Test-NetConnection -ComputerName your-slurm-host -Port 22

# Test SSH connection
ssh -v username@your-slurm-host
```

## 📊 Performance Optimization

### Windows-Specific Optimizations

1. **Antivirus Exclusions**
   - Add Python installation directory
   - Add dashboard project directory
   - Add temporary file locations

2. **Power Settings**
   - Set to "High Performance" for servers
   - Disable sleep mode for continuous operation

3. **Network Optimization**
   - Use wired connection for stability
   - Configure DNS for faster resolution

## 🔄 Windows Service Deployment

### Create Windows Service

For production deployment, run as Windows service:

1. **Install NSSM (Non-Sucking Service Manager)**
   ```powershell
   # Download from https://nssm.cc/
   # Or use Chocolatey
   choco install nssm
   ```

2. **Create Service**
   ```powershell
   # Create service
   nssm install SlurmDashboard "C:\Python39\python.exe" "C:\path\to\dashboard\main.py"
   
   # Configure service
   nssm set SlurmDashboard AppDirectory "C:\path\to\dashboard"
   nssm set SlurmDashboard DisplayName "SLURM Dashboard"
   nssm set SlurmDashboard Description "Multi-Cluster SLURM Dashboard"
   
   # Start service
   nssm start SlurmDashboard
   ```

### Service Management

```powershell
# Check service status
Get-Service SlurmDashboard

# Start/Stop service
Start-Service SlurmDashboard
Stop-Service SlurmDashboard

# Remove service
nssm remove SlurmDashboard confirm
```

## 📁 Windows File Structure

```
C:\SlurmDashboard\
├── main.py                    # Application entry point
├── app.py                     # Flask application
├── cluster_manager.py         # Multi-cluster management
├── slurm_manager.py          # SLURM interface (Windows-compatible)
├── config.py                 # Configuration
├── requirements.txt          # Python dependencies
├── .env                      # Environment configuration
├── clusters.json             # Cluster configurations
├── setup_windows.ps1         # Windows setup script
├── templates\                # HTML templates
├── static\                   # CSS, JS, images
└── logs\                     # Application logs
```

## 🎯 Windows Best Practices

### Development
- Use **PowerShell ISE** or **VS Code** for editing
- Enable **Windows Subsystem for Linux (WSL)** for better compatibility
- Use **Git for Windows** for version control

### Production
- Run as **Windows Service** for automatic startup
- Use **Task Scheduler** for maintenance tasks
- Implement **log rotation** for disk space management
- Set up **Windows Event Log** integration

### Security
- Use **Windows Firewall** for network security
- Implement **User Account Control** properly
- Regular **Windows Updates** for security patches
- **Backup** configuration files regularly

This Windows deployment guide ensures smooth operation of the SLURM Dashboard on Windows systems while maintaining all multi-cluster functionality and security features.
