#!/usr/bin/env python3
"""
Test the browser functionality
"""
import requests

def test_pages():
    """Test both the main page and test page"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Dashboard Pages...")
    print("=" * 50)
    
    # Test 1: Test page
    print("1. Testing /test page...")
    try:
        response = requests.get(f"{base_url}/test", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Test page loads successfully")
            print(f"   Content length: {len(response.text)} characters")
        else:
            print(f"   ❌ Test page failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Main dashboard page
    print("\n2. Testing / (main dashboard)...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Main dashboard loads successfully")
            print(f"   Content length: {len(response.text)} characters")
            
            # Check if it contains expected elements
            content = response.text.lower()
            if 'slurm dashboard' in content:
                print(f"   ✅ Contains SLURM dashboard title")
            if 'multi-cluster-dashboard.js' in content:
                print(f"   ✅ JavaScript file referenced")
            if 'dashboard.css' in content:
                print(f"   ✅ CSS file referenced")
        else:
            print(f"   ❌ Main dashboard failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: API endpoints
    print("\n3. Testing API endpoints...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        print(f"   /api/clusters: {response.status_code}")
        if response.status_code == 200:
            clusters = response.json()
            print(f"   ✅ Found {len(clusters)} clusters")
            
            if clusters:
                cluster_id = clusters[0]['id']
                data_response = requests.get(f"{base_url}/api/cluster/{cluster_id}/data", timeout=10)
                print(f"   /api/cluster/{cluster_id}/data: {data_response.status_code}")
                if data_response.status_code == 200:
                    data = data_response.json()
                    print(f"   ✅ Cluster data: {len(data.get('running_jobs', []))} running, {len(data.get('queue_jobs', []))} queued")
    except Exception as e:
        print(f"   ❌ API Error: {e}")

if __name__ == "__main__":
    test_pages()
