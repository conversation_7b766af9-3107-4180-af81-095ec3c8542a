#!/usr/bin/env python3
"""
Quick test script for cluster API functionality
"""

import requests
import json

def test_cluster_api():
    """Test the cluster management API"""
    base_url = "http://localhost:5000"
    
    print("Testing Cluster API...")
    
    # Test 1: Get clusters (should be empty initially)
    try:
        response = requests.get(f"{base_url}/api/clusters")
        if response.status_code == 200:
            clusters = response.json()
            print(f"✅ GET /api/clusters - Found {len(clusters)} clusters")
        else:
            print(f"❌ GET /api/clusters failed - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GET /api/clusters error: {e}")
        return False
    
    # Test 2: Add a test cluster
    test_cluster = {
        "name": "Test Cluster",
        "host": "test-host.example.com",
        "username": "testuser",
        "password": "testpass",
        "port": 22,
        "enabled": True,
        "description": "Test cluster for API validation"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/clusters",
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_cluster)
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                cluster_id = result.get('cluster_id')
                print(f"✅ POST /api/clusters - Cluster added with ID: {cluster_id}")
                
                # Test 3: Get clusters again (should have 1 now)
                response = requests.get(f"{base_url}/api/clusters")
                if response.status_code == 200:
                    clusters = response.json()
                    print(f"✅ GET /api/clusters - Now has {len(clusters)} clusters")
                    
                    # Test 4: Delete the test cluster
                    if cluster_id:
                        response = requests.delete(f"{base_url}/api/clusters/{cluster_id}")
                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                print("✅ DELETE /api/clusters - Test cluster deleted")
                                return True
                            else:
                                print(f"❌ DELETE failed: {result.get('error')}")
                        else:
                            print(f"❌ DELETE failed - Status: {response.status_code}")
                else:
                    print(f"❌ Second GET failed - Status: {response.status_code}")
            else:
                print(f"❌ POST failed: {result.get('error')}")
        else:
            print(f"❌ POST /api/clusters failed - Status: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ POST /api/clusters error: {e}")
        return False
    
    return False

if __name__ == "__main__":
    print("Cluster API Test")
    print("=" * 40)
    print("Make sure the dashboard is running on http://localhost:5000")
    print()
    
    success = test_cluster_api()
    
    print()
    print("=" * 40)
    if success:
        print("🎉 All tests passed! Add cluster functionality is working.")
    else:
        print("⚠️  Some tests failed. Check the dashboard logs for errors.")
    
    print()
    print("To test manually:")
    print("1. Open http://localhost:5000")
    print("2. Click 'Add Cluster'")
    print("3. Fill in the form and save")
    print("4. Check if cluster appears in dropdown")
