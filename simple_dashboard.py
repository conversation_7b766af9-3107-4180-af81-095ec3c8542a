#!/usr/bin/env python3
"""
Simple SLURM Dashboard - Direct Data Fetching
No background threads, no complex caching - just direct SSH calls
"""
from flask import Flask, render_template, jsonify, request
import json
import paramiko
import uuid

app = Flask(__name__)

# Simple cluster storage
CLUSTERS_FILE = 'clusters.json'

def load_clusters():
    """Load clusters from JSON file"""
    try:
        with open(CLUSTERS_FILE, 'r') as f:
            return json.load(f)
    except:
        return {}

def save_clusters(clusters):
    """Save clusters to JSON file"""
    with open(CLUSTERS_FILE, 'w') as f:
        json.dump(clusters, f, indent=2)

def ssh_command(host, username, password, command, port=22):
    """Execute SSH command using Paramiko - Simple and Direct"""
    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        ssh.connect(
            hostname=host,
            username=username,
            password=password,
            timeout=5,
            allow_agent=False,
            look_for_keys=False
        )

        stdin, stdout, stderr = ssh.exec_command(command)
        stdout.channel.settimeout(10.0)

        exit_status = stdout.channel.recv_exit_status()
        stdout_data = stdout.read().decode('utf-8', errors='ignore')
        stderr_data = stderr.read().decode('utf-8', errors='ignore')

        if exit_status == 0:
            return stdout_data.strip()
        else:
            print(f"Command failed: {command}")
            print(f"Error: {stderr_data}")
            return None

    except Exception as e:
        print(f"SSH error: {e}")
        return None
    finally:
        if ssh:
            try:
                ssh.close()
            except:
                pass

def get_cluster_data(cluster_id):
    """Get fresh cluster data directly"""
    clusters = load_clusters()
    if cluster_id not in clusters:
        return {'error': 'Cluster not found'}

    cluster = clusters[cluster_id]
    host = cluster['host']
    username = cluster['username']
    password = cluster['password']

    print(f"Fetching data for cluster {cluster['name']} ({host})")

    data = {
        'cluster_id': cluster_id,
        'name': cluster['name'],
        'host': host,
        'status': 'online',
        'queue_jobs': [],
        'running_jobs': [],
        'cluster_info': {}
    }

    try:
        # Get queue jobs
        queue_cmd = "squeue -t PD -o '%i|%j|%u|%T|%P|%Q|%r|%S' --noheader"
        queue_output = ssh_command(host, username, password, queue_cmd)
        if queue_output:
            for line in queue_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 8:
                        data['queue_jobs'].append({
                            'job_id': parts[0],
                            'name': parts[1],
                            'user': parts[2],
                            'state': parts[3],
                            'partition': parts[4],
                            'priority': parts[5],
                            'reason': parts[6],
                            'start_time': parts[7]
                        })

        # Get running jobs
        running_cmd = "squeue -t R -o '%i|%j|%u|%T|%P|%M|%N' --noheader"
        running_output = ssh_command(host, username, password, running_cmd)
        if running_output:
            for line in running_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 7:
                        data['running_jobs'].append({
                            'job_id': parts[0],
                            'name': parts[1],
                            'user': parts[2],
                            'state': parts[3],
                            'partition': parts[4],
                            'time': parts[5],
                            'nodes': parts[6]
                        })

        # Get cluster info
        info_cmd = "sinfo -N -o '%N|%T|%c|%m|%f' --noheader"
        info_output = ssh_command(host, username, password, info_cmd)
        nodes = {'total': 0, 'idle': 0, 'allocated': 0, 'mixed': 0}
        total_cpus = 0

        if info_output:
            for line in info_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 3:
                        nodes['total'] += 1
                        state = parts[1].lower()
                        if 'idle' in state:
                            nodes['idle'] += 1
                        elif 'alloc' in state:
                            nodes['allocated'] += 1
                        elif 'mix' in state:
                            nodes['mixed'] += 1

                        try:
                            cpus = int(parts[2])
                            total_cpus += cpus
                        except:
                            pass

        data['cluster_info'] = {
            'name': cluster['name'],
            'nodes': nodes,
            'total_cpus': total_cpus
        }

        print(f"Successfully fetched data: {len(data['queue_jobs'])} queue, {len(data['running_jobs'])} running")

    except Exception as e:
        print(f"Error fetching cluster data: {e}")
        data['error'] = str(e)
        data['status'] = 'error'

    return data

# Routes
@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('simple_dashboard.html')

@app.route('/api/clusters')
def api_clusters():
    """Get cluster list"""
    clusters = load_clusters()
    cluster_list = []

    for cluster_id, cluster in clusters.items():
        cluster_list.append({
            'id': cluster_id,
            'name': cluster['name'],
            'host': cluster['host'],
            'username': cluster['username'],
            'enabled': cluster.get('enabled', True),
            'status': 'offline',  # Will be updated when data is fetched
            'last_update': 'never',
            'description': cluster.get('description', ''),
            'queue_count': 0,
            'running_count': 0
        })

    return jsonify(cluster_list)

@app.route('/api/cluster/<cluster_id>/data')
def api_cluster_data(cluster_id):
    """Get cluster data - fetch fresh"""
    return jsonify(get_cluster_data(cluster_id))

@app.route('/api/cluster/<cluster_id>/running_jobs')
def api_running_jobs(cluster_id):
    """Get running jobs"""
    data = get_cluster_data(cluster_id)
    return jsonify(data.get('running_jobs', []))

@app.route('/api/cluster/<cluster_id>/queue_jobs')
def api_queue_jobs(cluster_id):
    """Get queue jobs"""
    data = get_cluster_data(cluster_id)
    return jsonify(data.get('queue_jobs', []))

@app.route('/api/cluster/<cluster_id>/info')
def api_cluster_info(cluster_id):
    """Get cluster info"""
    data = get_cluster_data(cluster_id)
    return jsonify(data.get('cluster_info', {}))

@app.route('/api/test-connection', methods=['POST'])
def api_test_connection():
    """Test connection"""
    data = request.get_json()
    host = data['host']
    username = data['username']
    password = data['password']

    # Test basic SSH
    result = ssh_command(host, username, password, 'echo "test"')
    if result == 'test':
        # Test SLURM
        slurm_result = ssh_command(host, username, password, 'squeue --version')
        if slurm_result:
            return jsonify({
                'success': True,
                'message': 'Connection successful!',
                'slurm_version': slurm_result
            })
        else:
            return jsonify({
                'success': False,
                'error': 'SSH works but SLURM not available'
            })
    else:
        return jsonify({
            'success': False,
            'error': 'SSH connection failed'
        })

@app.route('/api/clusters', methods=['POST'])
def api_add_cluster():
    """Add cluster"""
    data = request.get_json()

    clusters = load_clusters()
    cluster_id = str(uuid.uuid4())[:8]

    clusters[cluster_id] = {
        'name': data['name'],
        'host': data['host'],
        'username': data['username'],
        'password': data['password'],
        'port': data.get('port', 22),
        'enabled': data.get('enabled', True),
        'description': data.get('description', '')
    }

    save_clusters(clusters)

    return jsonify({
        'success': True,
        'cluster_id': cluster_id,
        'message': f"Cluster '{data['name']}' added successfully"
    })

if __name__ == '__main__':
    print("Starting Simple SLURM Dashboard...")
    print("Dashboard will be available at: http://localhost:5001")
    app.run(host='0.0.0.0', port=5001, debug=False)
