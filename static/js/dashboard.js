/**
 * SLURM Dashboard - Interactive JavaScript
 * Multi-Cluster Support
 */

class SlurmDashboard {
    constructor() {
        this.socket = null;
        this.sortable = null;
        this.lastUpdateTime = null;
        this.isConnected = false;
        this.currentCluster = null;
        this.clusters = [];
        this.editingCluster = null;

        this.init();
    }

    init() {
        this.initializeSocket();
        this.initializeEventListeners();
        this.initializeSortable();
        this.showLoading();
    }

    initializeSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.isConnected = true;
            this.updateConnectionStatus(true);
            this.hideLoading();
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.isConnected = false;
            this.updateConnectionStatus(false);
        });

        this.socket.on('dashboard_update', (data) => {
            this.updateDashboard(data);
        });

        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            this.updateConnectionStatus(false);
        });
    }

    initializeEventListeners() {
        // Manual refresh button
        document.getElementById('manual-refresh').addEventListener('click', () => {
            this.requestUpdate();
        });

        // Modal close
        document.getElementById('modal-close').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal on outside click
        document.getElementById('job-details-modal').addEventListener('click', (e) => {
            if (e.target.id === 'job-details-modal') {
                this.closeModal();
            }
        });

        // Escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    initializeSortable() {
        const queueList = document.getElementById('queue-list');

        this.sortable = Sortable.create(queueList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'dragging',
            onEnd: (evt) => {
                this.handlePriorityChange(evt);
            }
        });
    }

    handlePriorityChange(evt) {
        const jobElement = evt.item;
        const jobId = jobElement.dataset.jobId;
        const newIndex = evt.newIndex;
        const totalJobs = document.querySelectorAll('.queue-job').length;

        // Calculate new priority based on position
        // Higher position = higher priority
        const newPriority = totalJobs - newIndex;

        this.updateJobPriority(jobId, newPriority);
    }

    async updateJobPriority(jobId, newPriority) {
        try {
            this.showLoading();

            const response = await fetch('/api/update_priority', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    job_id: jobId,
                    priority: newPriority
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Priority updated successfully', 'success');
                // Update will come through socket
            } else {
                this.showNotification(`Failed to update priority: ${result.error}`, 'error');
                // Revert the change by requesting fresh data
                this.requestUpdate();
            }
        } catch (error) {
            console.error('Error updating priority:', error);
            this.showNotification('Error updating priority', 'error');
            this.requestUpdate();
        } finally {
            this.hideLoading();
        }
    }

    updateDashboard(data) {
        this.updateQueueJobs(data.queue_jobs || []);
        this.updateRunningJobs(data.running_jobs || []);
        this.updateClusterInfo(data.cluster_info || {});
        this.updateTimestamp();
    }

    updateQueueJobs(jobs) {
        const queueList = document.getElementById('queue-list');
        const queueCount = document.getElementById('queue-count');

        queueCount.textContent = jobs.length;

        if (jobs.length === 0) {
            queueList.innerHTML = '<div class="no-jobs">No jobs in queue</div>';
            return;
        }

        queueList.innerHTML = jobs.map(job => this.createQueueJobElement(job)).join('');

        // Add click listeners for job details
        queueList.querySelectorAll('.queue-job').forEach(jobElement => {
            jobElement.addEventListener('click', (e) => {
                if (!e.target.closest('.dragging')) {
                    this.showJobDetails(jobElement.dataset.jobId);
                }
            });
        });
    }

    createQueueJobElement(job) {
        const priorityClass = this.getPriorityClass(job.priority);
        const baseJobId = job.job_id;
        const displayJobId = job.display_job_id || job.job_id;
        const jobCount = job.job_count || 1;

        return `
            <div class="queue-job ${priorityClass}" data-job-id="${baseJobId}">
                <div class="job-header">
                    <span class="job-id">#${displayJobId}</span>
                    <span class="job-priority">${job.priority}</span>
                </div>
                <div class="job-name">${this.escapeHtml(job.name)}</div>
                <div class="job-details">
                    <span class="job-user">${this.escapeHtml(job.user)}</span>
                    <span class="job-partition">${this.escapeHtml(job.partition)}</span>
                    ${jobCount > 1 ? `<span class="job-count"><strong>Jobs:</strong> ${jobCount}</span>` : ''}
                </div>
                <div class="job-reason" title="${this.escapeHtml(job.reason)}">
                    ${this.escapeHtml(job.reason.substring(0, 30))}${job.reason.length > 30 ? '...' : ''}
                </div>
                <div class="priority-actions">
                    <button class="priority-btn" onclick="dashboard.setTopPriority('${baseJobId}')">🔝 Top Priority</button>
                    <button class="cancel-queue-btn" onclick="dashboard.cancelQueueJob('${baseJobId}', '${this.escapeHtml(job.name)}')">❌ Cancel${jobCount > 1 ? ` (${jobCount})` : ''}</button>
                </div>
            </div>
        `;
    }

    getPriorityClass(priority) {
        const priorityNum = parseInt(priority) || 0;
        if (priorityNum >= 1000) return 'high-priority';
        if (priorityNum >= 100) return 'medium-priority';
        return 'low-priority';
    }

    updateRunningJobs(jobs) {
        const tbody = document.getElementById('running-jobs-tbody');
        const runningCount = document.getElementById('running-count');

        runningCount.textContent = jobs.length;

        if (jobs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #7f8c8d;">No jobs currently running</td></tr>';
            return;
        }

        tbody.innerHTML = jobs.map(job => {
            const baseJobId = job.job_id;
            const displayJobId = job.display_job_id || job.job_id;
            const jobCount = job.job_count || 1;

            return `
                <tr>
                    <td><strong>#${displayJobId}</strong></td>
                    <td>${this.escapeHtml(job.name)}</td>
                    <td>${this.escapeHtml(job.user)}</td>
                    <td>${this.escapeHtml(job.partition)}</td>
                    <td>${this.escapeHtml(job.time)}</td>
                    <td>${this.escapeHtml(job.time_limit || 'N/A')}</td>
                    <td>${this.escapeHtml(job.nodes)}</td>
                    <td><button class="requeue-btn" onclick="dashboard.showRequeueConfirmation('${baseJobId}', '${this.escapeHtml(displayJobId)}', '${this.escapeHtml(job.name)}', '${this.escapeHtml(job.time || '-')}', ${jobCount})" title="Requeue ${jobCount} job(s)">🔄 Requeue (${jobCount})</button></td>
                </tr>
            `;
        }).join('');
    }

    updateClusterInfo(info) {
        if (!info.nodes) return;

        // Update node statistics
        document.getElementById('total-nodes').textContent = info.nodes.total || '--';
        document.getElementById('idle-nodes').textContent = info.nodes.idle || '--';
        document.getElementById('allocated-nodes').textContent = info.nodes.allocated || '--';
        document.getElementById('mixed-nodes').textContent = info.nodes.mixed || '--';
        document.getElementById('down-nodes').textContent = info.nodes.down || '--';

        // Update resource information
        document.getElementById('total-cpus').textContent = info.total_cpus || '--';
        document.getElementById('total-memory').textContent =
            info.total_memory_gb ? `${info.total_memory_gb} GB` : '--';

        // Update partitions
        this.updatePartitions(info.partitions || []);
    }

    updatePartitions(partitions) {
        const partitionsList = document.getElementById('partitions-list');

        if (partitions.length === 0) {
            partitionsList.innerHTML = '<div class="stat-row"><span>No partitions available</span></div>';
            return;
        }

        partitionsList.innerHTML = partitions.map(partition => `
            <div class="stat-row">
                <span class="stat-label">${this.escapeHtml(partition.name)}:</span>
                <span class="stat-value">${this.escapeHtml(partition.availability)}</span>
            </div>
        `).join('');
    }

    async showJobDetails(jobId) {
        try {
            this.showLoading();

            const response = await fetch(`/api/job_details/${jobId}`);
            const details = await response.json();

            if (response.ok) {
                this.displayJobDetails(details);
            } else {
                this.showNotification('Failed to load job details', 'error');
            }
        } catch (error) {
            console.error('Error fetching job details:', error);
            this.showNotification('Error loading job details', 'error');
        } finally {
            this.hideLoading();
        }
    }

    displayJobDetails(details) {
        const modal = document.getElementById('job-details-modal');
        const content = document.getElementById('job-details-content');

        const importantFields = [
            'JobId', 'JobName', 'UserId', 'JobState', 'Partition',
            'Priority', 'SubmitTime', 'StartTime', 'TimeLimit',
            'NumNodes', 'NumCPUs', 'MinMemoryNode', 'WorkDir'
        ];

        let html = '<div class="job-details-grid">';

        importantFields.forEach(field => {
            if (details[field]) {
                html += `
                    <div class="detail-row">
                        <span class="detail-label">${field}:</span>
                        <span class="detail-value">${this.escapeHtml(details[field])}</span>
                    </div>
                `;
            }
        });

        html += '</div>';

        // Add CSS for job details if not already present
        if (!document.querySelector('#job-details-styles')) {
            const style = document.createElement('style');
            style.id = 'job-details-styles';
            style.textContent = `
                .job-details-grid {
                    display: flex;
                    flex-direction: column;
                    gap: 0.8rem;
                }
                .detail-row {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.8rem;
                    background: #f8f9fa;
                    border-radius: 6px;
                    border-left: 3px solid #3498db;
                }
                .detail-label {
                    font-weight: 600;
                    color: #2c3e50;
                    min-width: 120px;
                }
                .detail-value {
                    color: #34495e;
                    word-break: break-all;
                }
            `;
            document.head.appendChild(style);
        }

        content.innerHTML = html;
        modal.style.display = 'block';
    }

    closeModal() {
        document.getElementById('job-details-modal').style.display = 'none';
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connection-status');
        const statusText = statusIndicator.querySelector('span');

        if (connected) {
            statusIndicator.classList.remove('disconnected');
            statusText.textContent = 'Connected';
        } else {
            statusIndicator.classList.add('disconnected');
            statusText.textContent = 'Disconnected';
        }
    }

    updateTimestamp() {
        const timestampElement = document.getElementById('last-update-time');
        const now = new Date();
        timestampElement.textContent = now.toLocaleTimeString();
        this.lastUpdateTime = now;
    }

    requestUpdate() {
        if (this.socket && this.isConnected) {
            this.socket.emit('request_update');
            this.showNotification('Refreshing data...', 'info');
        }
    }

    showLoading() {
        document.getElementById('loading-overlay').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 4000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(notification);
        }

        // Set notification style based on type
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        notification.style.background = colors[type] || colors.info;
        notification.textContent = message;

        // Show notification
        notification.style.transform = 'translateX(0)';

        // Hide after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(400px)';
        }, 3000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Requeue functionality
    showRequeueConfirmation(baseJobId, displayJobId, jobName, runtime, jobCount) {
        const jobText = jobCount === 1 ? 'job' : 'jobs';
        const message = `
⚠️ REQUEUE CONFIRMATION ⚠️

You are about to requeue running ${jobText}:
• Job ID: ${displayJobId}
• Name: ${jobName}
• Current Runtime: ${runtime}
• Number of ${jobText}: ${jobCount}

🔄 IMPORTANT RECOMMENDATION:
Before requeuing, consider adding your pending jobs to the queue first.
This ensures your requeued ${jobText} will have higher priority when restarted.

ℹ️ REQUEUE PROCESS:
1. All ${jobCount} ${jobText} will be requeued using 'scontrol requeue'
2. Then released using 'scontrol release' to start immediately

❓ Do you want to proceed with requeuing ${jobCount} ${jobText}?

✅ Click OK to requeue
❌ Click Cancel to abort`;

        if (confirm(message)) {
            this.requeueJob(baseJobId, displayJobId, jobCount);
        }
    }

    async requeueJob(baseJobId, displayJobId, jobCount) {
        try {
            this.showLoading();
            const jobText = jobCount === 1 ? 'job' : 'jobs';
            this.showNotification(`Requeuing ${jobCount} ${jobText} for ${displayJobId}...`, 'info');

            const response = await fetch(`/api/requeue_job`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    job_id: baseJobId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`✅ ${result.message}`, 'success');
                // Refresh data to show updated job status
                setTimeout(() => {
                    this.loadData();
                }, 3000); // Longer delay for requeue process
            } else {
                this.showNotification(`❌ Requeue failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error requeuing job:', error);
            this.showNotification('Error requeuing job: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async setTopPriority(jobId) {
        try {
            this.showLoading();
            const baseJobId = jobId.split('_')[0];
            this.showNotification(`Setting job ${baseJobId} to top priority...`, 'info');

            const response = await fetch(`/api/set_top_priority`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ job_id: jobId })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`✅ ${result.message}`, 'success');
                setTimeout(() => this.loadData(), 1000);
            } else {
                this.showNotification(`❌ Failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error setting top priority:', error);
            this.showNotification(`❌ Error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async cancelQueueJob(jobId, jobName) {
        const baseJobId = jobId.split('_')[0];

        const message = `
⚠️ CANCEL QUEUE JOB CONFIRMATION ⚠️

You are about to CANCEL queued job:
• Job ID: ${baseJobId}
• Name: ${jobName}

⚠️ WARNING:
This will remove the job from the queue permanently.
The job will NOT run and cannot be recovered.

ℹ️ CANCEL PROCESS:
Will execute 'sudo scancel ${baseJobId}' to remove from queue

❓ Are you sure you want to CANCEL this queued job?

✅ Click OK to cancel
❌ Click Cancel to abort`;

        if (!confirm(message)) {
            return;
        }

        try {
            this.showLoading();
            this.showNotification(`Canceling queued job ${baseJobId}...`, 'info');

            const response = await fetch(`/api/cancel_job`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    job_id: baseJobId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`✅ ${result.message}`, 'success');
                setTimeout(() => {
                    this.loadData();
                }, 1000);
            } else {
                this.showNotification(`❌ Cancel failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error canceling queue job:', error);
            this.showNotification('Error canceling job: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new SlurmDashboard();
});

// Handle page visibility changes to pause/resume updates
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Page hidden, pausing updates');
    } else {
        console.log('Page visible, resuming updates');
        if (window.dashboard && window.dashboard.isConnected) {
            window.dashboard.requestUpdate();
        }
    }
});
