/**
 * Multi-Cluster SLURM Dashboard - Interactive JavaScript
 */

class MultiClusterDashboard {
    constructor() {
        this.socket = null;
        this.sortable = null;
        this.lastUpdateTime = null;
        this.isConnected = false;
        this.currentCluster = null;
        this.clusters = [];
        this.editingCluster = null;
        this.hasShownWelcome = false;

        this.init();
    }

    init() {
        this.initializeSocket();
        this.initializeEventListeners();
        this.initializeSortable();
        this.loadClusters();
        this.showLoading();
    }

    initializeSocket() {
        // DISABLED: SocketIO functionality removed to fix loading issues
        console.log('SocketIO disabled - using REST API only');
        this.isConnected = true;
        this.updateConnectionStatus(true);
        this.hideLoading();

        // Start polling for updates instead of using WebSocket
        this.startPolling();
    }

    initializeEventListeners() {
        // Manual refresh button
        document.getElementById('manual-refresh').addEventListener('click', () => {
            this.requestUpdate();
        });

        // Cluster selector
        document.getElementById('cluster-select').addEventListener('change', (e) => {
            this.selectCluster(e.target.value);
        });

        // Add cluster button
        document.getElementById('add-cluster-btn').addEventListener('click', () => {
            this.showAddClusterModal();
        });

        // Modal close buttons
        document.getElementById('modal-close').addEventListener('click', () => {
            this.closeModal('job-details-modal');
        });

        document.getElementById('cluster-modal-close').addEventListener('click', () => {
            this.closeModal('cluster-modal');
        });

        document.getElementById('cluster-panel-close').addEventListener('click', () => {
            this.closeClusterPanel();
        });

        // Cluster form buttons (prevent form submission)
        document.getElementById('save-cluster-btn').addEventListener('click', () => {
            this.saveCluster();
        });

        document.getElementById('test-connection-btn').addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.testClusterConnection();
        });

        document.getElementById('cancel-cluster-btn').addEventListener('click', () => {
            this.closeModal('cluster-modal');
        });

        // Close modals on outside click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal('job-details-modal');
                this.closeModal('cluster-modal');
                this.closeClusterPanel();
            }
        });

        // Show cluster management on header click
        document.querySelector('.dashboard-header h1').addEventListener('click', () => {
            this.showClusterPanel();
        });
    }

    initializeSortable() {
        const queueList = document.getElementById('queue-list');

        this.sortable = Sortable.create(queueList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'dragging',
            onEnd: (evt) => {
                this.handlePriorityChange(evt);
            }
        });
    }

    async loadClusters() {
        try {
            const response = await fetch('/api/clusters');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.clusters = await response.json();
            this.updateClusterSelector();

            // Only update panel if it's open
            if (document.getElementById('cluster-panel').classList.contains('open')) {
                this.updateClusterPanel();
            }

            // Auto-select first cluster if available and no cluster currently selected
            if (this.clusters.length > 0 && !this.currentCluster) {
                this.selectCluster(this.clusters[0].id);
            }
        } catch (error) {
            console.error('Error loading clusters:', error);
            this.showNotification('Failed to load clusters', 'error');
        }
    }

    updateClusterSelector() {
        const select = document.getElementById('cluster-select');

        if (this.clusters.length === 0) {
            select.innerHTML = '<option value="">No clusters configured - Add one!</option>';
            this.updateCurrentClusterInfo('Click "Add Cluster" to get started');
        } else {
            select.innerHTML = '<option value="">Select Cluster...</option>';

            this.clusters.forEach((cluster, index) => {
                const option = document.createElement('option');
                option.value = cluster.id;

                // Add "Default" badge to the first cluster
                const defaultBadge = index === 0 ? ' [Default]' : '';
                option.textContent = `${cluster.name} (${cluster.status})${defaultBadge}`;

                if (cluster.id === this.currentCluster) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    }

    selectCluster(clusterId) {
        if (!clusterId) {
            this.currentCluster = null;
            this.clearDashboard();
            this.updateCurrentClusterInfo('No cluster selected');
            return;
        }

        this.currentCluster = clusterId;
        const cluster = this.clusters.find(c => c.id === clusterId);
        if (cluster) {
            this.updateCurrentClusterInfo(`Connected to: ${cluster.name}`);
        }
        this.updateClusterSelector();
        this.requestClusterUpdate(clusterId);
    }

    updateCurrentClusterInfo(message) {
        const infoElement = document.getElementById('current-cluster-info');
        if (infoElement) {
            infoElement.innerHTML = `<small>${message}</small>`;
        }
    }

    async requestClusterUpdate(clusterId) {
        if (!clusterId) return;

        try {
            const response = await fetch(`/api/cluster/${clusterId}/data`);
            const data = await response.json();
            this.updateClusterDashboard(data);
        } catch (error) {
            console.error('Error fetching cluster data:', error);
            this.showNotification('Error fetching cluster data', 'error');
        }
    }

    updateDashboard(data) {
        // Update cluster list
        if (data.cluster_list) {
            this.clusters = data.cluster_list;
            this.updateClusterSelector();
            this.updateClusterPanel();
        }

        // Update current cluster data
        if (this.currentCluster && data.clusters && data.clusters[this.currentCluster]) {
            this.updateClusterDashboard(data.clusters[this.currentCluster]);
        }

        this.updateTimestamp();
    }

    updateClusterDashboard(data) {
        this.updateQueueJobs(data.queue_jobs || []);
        this.updateRunningJobs(data.running_jobs || []);
        this.updateClusterInfo(data.cluster_info || {});
    }

    updateQueueJobs(jobs) {
        const queueList = document.getElementById('queue-list');
        const queueCount = document.getElementById('queue-count');

        queueCount.textContent = jobs.length;

        if (jobs.length === 0) {
            queueList.innerHTML = '<div class="no-jobs">No jobs in queue</div>';
            return;
        }

        queueList.innerHTML = jobs.map(job => this.createQueueJobElement(job)).join('');

        // Add click listeners for job details
        queueList.querySelectorAll('.queue-job').forEach(jobElement => {
            jobElement.addEventListener('click', (e) => {
                if (!e.target.closest('.dragging')) {
                    this.showJobDetails(jobElement.dataset.jobId);
                }
            });
        });
    }

    createQueueJobElement(job) {
        const priorityClass = this.getPriorityClass(job.priority);

        return `
            <div class="queue-job ${priorityClass}" data-job-id="${job.job_id}">
                <div class="job-header">
                    <span class="job-id">#${job.job_id}</span>
                    <span class="job-priority">${job.priority}</span>
                </div>
                <div class="job-name">${this.escapeHtml(job.name)}</div>
                <div class="job-details">
                    <span class="job-user">${this.escapeHtml(job.user)}</span>
                    <span class="job-partition">${this.escapeHtml(job.partition)}</span>
                </div>
                <div class="job-reason" title="${this.escapeHtml(job.reason)}">
                    ${this.escapeHtml(job.reason.substring(0, 30))}${job.reason.length > 30 ? '...' : ''}
                </div>
            </div>
        `;
    }

    getPriorityClass(priority) {
        const priorityNum = parseInt(priority) || 0;
        if (priorityNum >= 1000) return 'high-priority';
        if (priorityNum >= 100) return 'medium-priority';
        return 'low-priority';
    }

    updateRunningJobs(jobs) {
        const tbody = document.getElementById('running-jobs-tbody');
        const runningCount = document.getElementById('running-count');

        runningCount.textContent = jobs.length;

        if (jobs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #7f8c8d;">No jobs currently running</td></tr>';
            return;
        }

        tbody.innerHTML = jobs.map(job => {
            const baseJobId = job.job_id;
            const displayJobId = job.display_job_id || job.job_id;
            const jobCount = job.job_count || 1;

            return `
                <tr>
                    <td><strong>#${displayJobId}</strong></td>
                    <td>${this.escapeHtml(job.name)}</td>
                    <td>${this.escapeHtml(job.user)}</td>
                    <td>${this.escapeHtml(job.partition)}</td>
                    <td>${this.escapeHtml(job.time)}</td>
                    <td>${this.escapeHtml(job.time_limit || 'N/A')}</td>
                    <td>${this.escapeHtml(job.nodes)}</td>
                    <td><button class="requeue-btn" onclick="dashboard.showRequeueConfirmation('${baseJobId}', '${this.escapeHtml(displayJobId)}', '${this.escapeHtml(job.name)}', '${this.escapeHtml(job.time || '-')}', ${jobCount})" title="Requeue ${jobCount} job(s)">🔄 Requeue (${jobCount})</button></td>
                </tr>
            `;
        }).join('');
    }

    updateClusterInfo(info) {
        if (!info.nodes) return;

        // Update node statistics
        document.getElementById('total-nodes').textContent = info.nodes.total || '--';
        document.getElementById('idle-nodes').textContent = info.nodes.idle || '--';
        document.getElementById('allocated-nodes').textContent = info.nodes.allocated || '--';
        document.getElementById('mixed-nodes').textContent = info.nodes.mixed || '--';
        document.getElementById('down-nodes').textContent = info.nodes.down || '--';

        // Update resource information
        document.getElementById('total-cpus').textContent = info.total_cpus || '--';
        document.getElementById('total-memory').textContent =
            info.total_memory_gb ? `${info.total_memory_gb} GB` : '--';

        // Update partitions
        this.updatePartitions(info.partitions || []);
    }

    updatePartitions(partitions) {
        const partitionsList = document.getElementById('partitions-list');

        if (partitions.length === 0) {
            partitionsList.innerHTML = '<div class="stat-row"><span>No partitions available</span></div>';
            return;
        }

        partitionsList.innerHTML = partitions.map(partition => `
            <div class="stat-row">
                <span class="stat-label">${this.escapeHtml(partition.name)}:</span>
                <span class="stat-value">${this.escapeHtml(partition.availability)}</span>
            </div>
        `).join('');
    }

    handlePriorityChange(evt) {
        if (!this.currentCluster) {
            this.showNotification('Please select a cluster first', 'warning');
            return;
        }

        const jobElement = evt.item;
        const jobId = jobElement.dataset.jobId;
        const newIndex = evt.newIndex;
        const totalJobs = document.querySelectorAll('.queue-job').length;

        // Calculate new priority based on position
        const newPriority = totalJobs - newIndex;

        this.updateJobPriority(jobId, newPriority);
    }

    async updateJobPriority(jobId, newPriority) {
        if (!this.currentCluster) return;

        try {
            this.showLoading();

            const response = await fetch(`/api/cluster/${this.currentCluster}/update_priority`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    job_id: jobId,
                    priority: newPriority
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Priority updated successfully', 'success');
                this.requestClusterUpdate(this.currentCluster);
            } else {
                this.showNotification(`Failed to update priority: ${result.error}`, 'error');
                this.requestClusterUpdate(this.currentCluster);
            }
        } catch (error) {
            console.error('Error updating priority:', error);
            this.showNotification('Error updating priority', 'error');
            this.requestClusterUpdate(this.currentCluster);
        } finally {
            this.hideLoading();
        }
    }

    async showJobDetails(jobId) {
        if (!this.currentCluster) return;

        try {
            this.showLoading();

            const response = await fetch(`/api/cluster/${this.currentCluster}/job_details/${jobId}`);
            const details = await response.json();

            if (response.ok) {
                this.displayJobDetails(details);
            } else {
                this.showNotification('Failed to load job details', 'error');
            }
        } catch (error) {
            console.error('Error fetching job details:', error);
            this.showNotification('Error loading job details', 'error');
        } finally {
            this.hideLoading();
        }
    }

    displayJobDetails(details) {
        const modal = document.getElementById('job-details-modal');
        const content = document.getElementById('job-details-content');

        const importantFields = [
            'JobId', 'JobName', 'UserId', 'JobState', 'Partition',
            'Priority', 'SubmitTime', 'StartTime', 'TimeLimit',
            'NumNodes', 'NumCPUs', 'MinMemoryNode', 'WorkDir'
        ];

        let html = '<div class="job-details-grid">';

        importantFields.forEach(field => {
            if (details[field]) {
                html += `
                    <div class="detail-row">
                        <span class="detail-label">${field}:</span>
                        <span class="detail-value">${this.escapeHtml(details[field])}</span>
                    </div>
                `;
            }
        });

        html += '</div>';

        content.innerHTML = html;
        modal.style.display = 'block';
    }

    // Cluster Management Methods
    showAddClusterModal() {
        this.editingCluster = null;

        // Set appropriate title based on whether this is the first cluster
        const isFirstCluster = this.clusters.length === 0;
        const title = isFirstCluster ? 'Add Your First Cluster (Default)' : 'Add New Cluster';
        document.getElementById('cluster-modal-title').textContent = title;

        document.getElementById('cluster-form').reset();
        document.getElementById('cluster-modal').style.display = 'block';
    }

    showEditClusterModal(clusterId) {
        const cluster = this.clusters.find(c => c.id === clusterId);
        if (!cluster) return;

        this.editingCluster = clusterId;
        document.getElementById('cluster-modal-title').textContent = 'Edit Cluster';

        // Populate form
        document.getElementById('cluster-name').value = cluster.name;
        document.getElementById('cluster-host').value = cluster.host;
        document.getElementById('cluster-username').value = cluster.username;
        document.getElementById('cluster-password').value = ''; // Don't show password
        document.getElementById('cluster-port').value = cluster.port || 22;
        document.getElementById('cluster-description').value = cluster.description || '';
        document.getElementById('cluster-enabled').checked = cluster.enabled;

        document.getElementById('cluster-modal').style.display = 'block';
    }

    async saveCluster() {
        const form = document.getElementById('cluster-form');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Validate required fields
        if (!data.name || !data.host || !data.username || !data.password) {
            this.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Auto-generate cluster name if empty
        if (!data.name.trim()) {
            data.name = `${data.username}@${data.host}`;
        }

        data.enabled = document.getElementById('cluster-enabled').checked;
        data.port = parseInt(data.port) || 22;

        try {
            this.showLoading();

            const isFirstCluster = this.clusters.length === 0 && !this.editingCluster;

            let response;
            if (this.editingCluster) {
                response = await fetch(`/api/clusters/${this.editingCluster}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
            } else {
                response = await fetch('/api/clusters', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                const message = result.message || (this.editingCluster ? 'Cluster updated!' : 'Cluster added!');
                this.showNotification(message, 'success');
                this.closeModal('cluster-modal');

                // Reload clusters quickly
                this.loadClusters();

                // If this was the first cluster or a new cluster, automatically select it
                if ((isFirstCluster || !this.editingCluster) && result.cluster_id) {
                    setTimeout(() => {
                        this.selectCluster(result.cluster_id);
                        this.showNotification('Cluster selected and fetching data...', 'info');
                    }, 500);
                } else if (this.editingCluster && this.currentCluster === this.editingCluster) {
                    // If editing current cluster, refresh its data
                    setTimeout(() => {
                        this.requestClusterUpdate(this.currentCluster);
                    }, 500);
                }
            } else {
                this.showNotification(`Error: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error saving cluster:', error);
            this.showNotification(`Failed to save cluster: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async deleteCluster(clusterId) {
        if (!confirm('Are you sure you want to delete this cluster?')) return;

        try {
            this.showLoading();

            const response = await fetch(`/api/clusters/${clusterId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Cluster deleted successfully', 'success');
                if (this.currentCluster === clusterId) {
                    this.currentCluster = null;
                    this.clearDashboard();
                }
                this.loadClusters();
            } else {
                this.showNotification(`Error: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error deleting cluster:', error);
            this.showNotification('Error deleting cluster', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async testClusterConnection() {
        const formData = new FormData(document.getElementById('cluster-form'));
        const data = Object.fromEntries(formData.entries());

        // Only need host, username, password for connection test
        if (!data.host || !data.username || !data.password) {
            this.showNotification('Please fill in Host, Username, and Password to test connection', 'warning');
            return;
        }

        const testBtn = document.getElementById('test-connection-btn');
        const originalText = testBtn.innerHTML;

        try {
            // Update button to show testing state
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            testBtn.disabled = true;

            // Test connection with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

            const response = await fetch('/api/test-connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    host: data.host,
                    username: data.username,
                    password: data.password,
                    port: parseInt(data.port) || 22
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                this.showNotification('✅ Network connectivity successful!', 'success');
                if (result.note) {
                    this.showNotification(result.note, 'info');
                }
                if (result.slurm_version) {
                    this.showNotification(`SLURM version: ${result.slurm_version}`, 'info');
                }
            } else {
                this.showNotification(`❌ Connection test failed: ${result.error}`, 'error');
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                this.showNotification('⏱️ Connection test timed out (15s)', 'error');
            } else {
                console.error('Error testing connection:', error);
                this.showNotification(`❌ Connection test failed: ${error.message}`, 'error');
            }
        } finally {
            // Restore button
            testBtn.innerHTML = originalText;
            testBtn.disabled = false;
        }
    }

    showClusterPanel() {
        document.getElementById('cluster-panel').classList.add('open');
        this.updateClusterPanel();
    }

    closeClusterPanel() {
        document.getElementById('cluster-panel').classList.remove('open');
    }

    updateClusterPanel() {
        const clusterList = document.getElementById('cluster-list');

        if (this.clusters.length === 0) {
            clusterList.innerHTML = '<div class="no-clusters">No clusters configured</div>';
            return;
        }

        clusterList.innerHTML = this.clusters.map((cluster, index) => {
            const isDefault = index === 0;
            const defaultBadge = isDefault ? '<span class="default-badge"><i class="fas fa-star"></i> Default</span>' : '';

            return `
            <div class="cluster-item ${cluster.status} ${cluster.id === this.currentCluster ? 'active' : ''} ${isDefault ? 'default-cluster' : ''}">
                <div class="cluster-item-header">
                    <div class="cluster-name">
                        ${this.escapeHtml(cluster.name)}
                        ${defaultBadge}
                    </div>
                    <div class="cluster-status ${cluster.status}">${cluster.status}</div>
                </div>
                <div class="cluster-info">
                    <div>${this.escapeHtml(cluster.host)} (${this.escapeHtml(cluster.username)})</div>
                    <div>${this.escapeHtml(cluster.description || 'No description')}</div>
                </div>
                <div class="cluster-stats">
                    <span>Queue: ${cluster.queue_count}</span>
                    <span>Running: ${cluster.running_count}</span>
                </div>
                <div class="cluster-actions">
                    <button class="edit-cluster-btn" onclick="dashboard.showEditClusterModal('${cluster.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="delete-cluster-btn" onclick="dashboard.deleteCluster('${cluster.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                    <button class="test-cluster-btn" onclick="dashboard.selectCluster('${cluster.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </div>
            `;
        }).join('');
    }

    clearDashboard() {
        const hasNoClusters = this.clusters.length === 0;

        if (hasNoClusters) {
            document.getElementById('queue-list').innerHTML = `
                <div class="no-jobs welcome-message">
                    <h3><i class="fas fa-rocket"></i> Welcome to SLURM Dashboard!</h3>
                    <p>Get started by adding your first cluster:</p>
                    <ol>
                        <li>Click the <strong>"Add Cluster"</strong> button above</li>
                        <li>Enter your cluster details (name, host, username, password)</li>
                        <li>Test the connection (optional)</li>
                        <li>Save - it will become your default cluster!</li>
                    </ol>
                    <p><small><i class="fas fa-star"></i> Your first cluster will be automatically selected as default.<br>
                    You can add more clusters later and switch between them easily.</small></p>
                </div>
            `;
            document.getElementById('running-jobs-tbody').innerHTML = '<tr><td colspan="6" style="text-align: center; color: #7f8c8d;">Add a cluster to view running jobs</td></tr>';
        } else {
            document.getElementById('queue-list').innerHTML = '<div class="no-jobs">Select a cluster to view jobs</div>';
            document.getElementById('running-jobs-tbody').innerHTML = '<tr><td colspan="6" style="text-align: center; color: #7f8c8d;">Select a cluster to view running jobs</td></tr>';
        }

        document.getElementById('queue-count').textContent = '0';
        document.getElementById('running-count').textContent = '0';

        // Clear cluster info
        ['total-nodes', 'idle-nodes', 'allocated-nodes', 'mixed-nodes', 'down-nodes', 'total-cpus', 'total-memory'].forEach(id => {
            document.getElementById(id).textContent = '--';
        });

        const message = hasNoClusters ? 'Add your first cluster to get started' : 'No cluster selected';
        document.getElementById('partitions-list').innerHTML = `<div class="stat-row"><span>${message}</span></div>`;
    }

    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connection-status');
        const statusText = statusIndicator.querySelector('span');

        if (connected) {
            statusIndicator.classList.remove('disconnected');
            statusText.textContent = 'Connected';
        } else {
            statusIndicator.classList.add('disconnected');
            statusText.textContent = 'Disconnected';
        }
    }

    updateTimestamp() {
        const timestampElement = document.getElementById('last-update-time');
        const now = new Date();
        timestampElement.textContent = now.toLocaleTimeString();
        this.lastUpdateTime = now;
    }

    startPolling() {
        // Poll for updates every 30 seconds
        setInterval(() => {
            if (this.currentCluster) {
                this.requestClusterUpdate(this.currentCluster);
            }
        }, 30000);
    }

    requestUpdate() {
        // DISABLED: SocketIO removed, using direct API calls
        if (this.currentCluster) {
            this.requestClusterUpdate(this.currentCluster);
            this.showNotification('Refreshing data...', 'info');
        } else {
            this.loadClusters();
            this.showNotification('Refreshing cluster list...', 'info');
        }
    }

    showLoading() {
        document.getElementById('loading-overlay').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 4000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(notification);
        }

        // Set notification style based on type
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        notification.style.background = colors[type] || colors.info;
        notification.textContent = message;

        // Show notification
        notification.style.transform = 'translateX(0)';

        // Hide after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(400px)';
        }, 3000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Requeue functionality
    showRequeueConfirmation(baseJobId, displayJobId, jobName, runtime, jobCount) {
        const jobText = jobCount === 1 ? 'job' : 'jobs';
        const message = `
⚠️ REQUEUE CONFIRMATION ⚠️

You are about to requeue running ${jobText}:
• Job ID: ${displayJobId}
• Name: ${jobName}
• Current Runtime: ${runtime}
• Number of ${jobText}: ${jobCount}

🔄 IMPORTANT RECOMMENDATION:
Before requeuing, consider adding your pending jobs to the queue first.
This ensures your requeued ${jobText} will have higher priority when restarted.

ℹ️ REQUEUE PROCESS:
1. All ${jobCount} ${jobText} will be requeued using 'scontrol requeue'
2. Then released using 'scontrol release' to start immediately

❓ Do you want to proceed with requeuing ${jobCount} ${jobText}?

✅ Click OK to requeue
❌ Click Cancel to abort`;

        if (confirm(message)) {
            this.requeueJob(baseJobId, displayJobId, jobCount);
        }
    }

    async requeueJob(baseJobId, displayJobId, jobCount) {
        if (!this.currentCluster) {
            this.showNotification('No cluster selected', 'error');
            return;
        }

        try {
            this.showLoading();
            const jobText = jobCount === 1 ? 'job' : 'jobs';
            this.showNotification(`Requeuing ${jobCount} ${jobText} for ${displayJobId}...`, 'info');

            const response = await fetch(`/api/cluster/${this.currentCluster}/requeue_job`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    job_id: baseJobId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(`✅ ${result.message}`, 'success');
                // Refresh data to show updated job status
                setTimeout(() => {
                    this.requestClusterUpdate(this.currentCluster);
                }, 3000); // Longer delay for requeue process
            } else {
                this.showNotification(`❌ Requeue failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error requeuing job:', error);
            this.showNotification('Error requeuing job: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new MultiClusterDashboard();
});

// Handle page visibility changes to pause/resume updates
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Page hidden, pausing updates');
    } else {
        console.log('Page visible, resuming updates');
        if (window.dashboard && window.dashboard.isConnected) {
            window.dashboard.requestUpdate();
        }
    }
});
