/* SLURM Dashboard - Professional Styling */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-header h1 i {
    color: #3498db;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

/* Cluster Selector */
.cluster-selector select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
    min-width: 200px;
    cursor: pointer;
}

.cluster-selector select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.add-cluster-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-cluster-btn:hover {
    background: #229954;
    transform: translateY(-1px);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #27ae60;
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-indicator.disconnected {
    background: #e74c3c;
}

.status-indicator i {
    font-size: 0.8rem;
}

.last-update {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.refresh-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.7rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.refresh-btn:hover {
    background: #2980b9;
    transform: rotate(180deg);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    gap: 2rem;
    padding: 2rem;
}

/* Sidebar - Queue Management */
.sidebar {
    width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: fit-content;
    max-height: calc(100vh - 140px);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.sidebar-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.queue-stats {
    font-size: 0.9rem;
    opacity: 0.9;
}

.queue-controls {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.priority-legend {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.priority-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.priority-color.high {
    background: #e74c3c;
}

.priority-color.medium {
    background: #f39c12;
}

.priority-color.low {
    background: #95a5a6;
}

.queue-container {
    flex: 1;
    overflow-y: auto;
    max-height: 500px;
}

.queue-list {
    padding: 1rem;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    margin: 1rem 0;
}

.welcome-message h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.welcome-message h3 i {
    color: #3498db;
    margin-right: 0.5rem;
}

.welcome-message p {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.welcome-message ol {
    text-align: left;
    max-width: 300px;
    margin: 1rem auto;
    color: #495057;
}

.welcome-message ol li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.welcome-message small {
    color: #6c757d;
    font-style: italic;
}

/* Queue Job Items */
.queue-job {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.8rem;
    cursor: move;
    transition: all 0.3s ease;
    position: relative;
}

.queue-job:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.queue-job.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.queue-job.high-priority {
    border-left: 4px solid #e74c3c;
}

.queue-job.medium-priority {
    border-left: 4px solid #f39c12;
}

.queue-job.low-priority {
    border-left: 4px solid #95a5a6;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.job-id {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.job-priority {
    background: #3498db;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.job-name {
    font-size: 0.9rem;
    color: #34495e;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.job-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.job-user {
    font-weight: 500;
}

.job-partition {
    background: #ecf0f1;
    padding: 0.1rem 0.4rem;
    border-radius: 4px;
}

/* Main Dashboard Area */
.dashboard-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Cluster Overview */
.cluster-overview {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.cluster-overview h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.overview-card {
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content {
    padding: 1.5rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: #7f8c8d;
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
}

.stat-value.idle {
    color: #27ae60;
}

.stat-value.allocated {
    color: #e74c3c;
}

.stat-value.mixed {
    color: #f39c12;
}

.stat-value.down {
    color: #95a5a6;
}

/* Running Jobs Section */
.running-jobs {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.running-stats {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.jobs-table-container {
    overflow-x: auto;
}

.jobs-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.jobs-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
}

.jobs-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    font-size: 0.9rem;
}

.jobs-table tbody tr:hover {
    background: #f8f9fa;
    cursor: pointer;
}

.jobs-table tbody tr:last-child td {
    border-bottom: none;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-help {
    display: block;
    margin-top: 0.3rem;
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* Cluster Panel */
.cluster-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1500;
    display: flex;
    flex-direction: column;
}

.cluster-panel.open {
    right: 0;
}

.cluster-panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cluster-panel-header h3 {
    margin: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.panel-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.cluster-panel-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.cluster-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.cluster-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cluster-item.active {
    border-color: #3498db;
    background: #e3f2fd;
}

.cluster-item.offline {
    border-color: #e74c3c;
    background: #ffebee;
}

.cluster-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.cluster-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.default-badge {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.default-cluster {
    border-left: 4px solid #f39c12;
}

.cluster-status {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.cluster-status.online {
    background: #d4edda;
    color: #155724;
}

.cluster-status.offline {
    background: #f8d7da;
    color: #721c24;
}

.cluster-status.error {
    background: #fff3cd;
    color: #856404;
}

.cluster-info {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.cluster-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #495057;
}

.cluster-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.cluster-actions button {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-cluster-btn {
    background: #3498db;
    color: white;
}

.edit-cluster-btn:hover {
    background: #2980b9;
}

.delete-cluster-btn {
    background: #e74c3c;
    color: white;
}

.delete-cluster-btn:hover {
    background: #c0392b;
}

.test-cluster-btn {
    background: #f39c12;
    color: white;
}

.test-cluster-btn:hover {
    background: #e67e22;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 3000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #667eea;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 400px;
    }

    .overview-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
        gap: 1rem;
    }

    .dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.queue-job {
    animation: fadeIn 0.3s ease;
}

/* Sortable.js specific styles */
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Custom scrollbar */
.queue-container::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.queue-container::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.queue-container::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.queue-container::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Requeue Button Styles */
.requeue-btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    min-width: 80px;
    justify-content: center;
}

.requeue-btn:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.requeue-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(243, 156, 18, 0.3);
}

.requeue-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Priority Button Styles (for consistency) */
.priority-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    min-width: 100px;
    justify-content: center;
}

.priority-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f4e79);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.priority-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
}

/* Job Action Dropdown Styles */
.job-action-dropdown {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    border: none;
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.job-action-dropdown:hover {
    background: linear-gradient(135deg, #2c3e50, #1a252f);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 73, 94, 0.3);
}

.job-action-dropdown:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.job-action-dropdown option {
    background: #2c3e50;
    color: white;
    padding: 0.5rem;
}

/* Running Jobs Header Styles */
.running-jobs-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.running-jobs-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Cancel Queue Job Button Styles */
.cancel-queue-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    min-width: 80px;
    justify-content: center;
    margin-left: 0.5rem;
}

.cancel-queue-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.cancel-queue-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(231, 76, 60, 0.3);
}

.cancel-queue-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Update priority actions to handle multiple buttons */
.priority-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}
