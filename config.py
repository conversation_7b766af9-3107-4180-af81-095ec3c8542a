"""
Configuration settings for SLURM Dashboard
Supports both local and Docker deployment
"""
import os

class Config:
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'slurm-dashboard-secret-key-2024'
    DEBUG = os.environ.get('FLASK_ENV', 'development') == 'development'

    # SLURM settings
    SLURM_USER = os.environ.get('SLURM_USER') or 'slurmadmin'  # User with sudo privileges
    SLURM_HOST = os.environ.get('SLURM_HOST') or 'localhost'  # SLURM host for SSH connection
    REFRESH_INTERVAL = int(os.environ.get('REFRESH_INTERVAL', '10'))  # seconds

    # SSH settings for remote SLURM access
    SSH_PASSWORD = os.environ.get('SLURM_SSH_PASSWORD') or ''
    SSH_TIMEOUT = int(os.environ.get('SSH_TIMEOUT', '30'))  # seconds

    # Dashboard settings
    MAX_QUEUE_JOBS = int(os.environ.get('MAX_QUEUE_JOBS', '100'))
    MAX_RUNNING_JOBS = int(os.environ.get('MAX_RUNNING_JOBS', '500'))  # Increased to handle large clusters

    # Cluster information
    CLUSTER_NAME = os.environ.get('CLUSTER_NAME') or 'HPC Cluster'

    # Docker-specific settings
    DOCKER_MODE = os.environ.get('DOCKER_MODE', 'false').lower() == 'true'
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')

    # Network settings
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', '5000'))
