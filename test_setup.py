#!/usr/bin/env python3
"""
SLURM Dashboard Setup Test
Tests the basic functionality and dependencies
"""

import sys
import subprocess
import importlib

def test_python_version():
    """Test Python version compatibility"""
    print("Testing Python version...")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required. Current version:", sys.version)
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def test_dependencies():
    """Test required Python packages"""
    print("\nTesting Python dependencies...")
    required_packages = ['flask', 'flask_socketio', 'psutil']

    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} - installed")
        except ImportError:
            print(f"❌ {package} - not installed")
            return False
    return True

def test_slurm_commands():
    """Test SLURM command availability"""
    print("\nTesting SLURM commands...")
    commands = ['squeue', 'sinfo', 'scontrol']

    all_available = True
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '--version'],
                                  capture_output=True,
                                  text=True,
                                  timeout=5)
            if result.returncode == 0:
                print(f"✅ {cmd} - available")
            else:
                print(f"❌ {cmd} - not working properly")
                all_available = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"⚠️  {cmd} - not found (expected if not on SLURM system)")
            # Don't mark as failed since this might be running on non-SLURM system

    if not all_available:
        print("ℹ️  SLURM commands not available locally. This is normal if:")
        print("   - Running on non-SLURM system (will use SSH to connect)")
        print("   - Using Docker deployment")

    return True  # Don't fail the test for missing SLURM commands

def test_config():
    """Test configuration file"""
    print("\nTesting configuration...")
    try:
        from config import Config
        config = Config()
        print(f"✅ Configuration loaded")
        print(f"   - Cluster name: {config.CLUSTER_NAME}")
        print(f"   - SLURM user: {config.SLURM_USER}")
        print(f"   - Refresh interval: {config.REFRESH_INTERVAL}s")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_ssh_client():
    """Test SSH client availability"""
    print("\nTesting SSH client...")
    try:
        result = subprocess.run(['ssh', '-V'],
                              capture_output=True,
                              text=True,
                              timeout=5)
        if result.returncode == 0 or 'OpenSSH' in result.stderr:
            print("✅ SSH client available")
            return True
        else:
            print("⚠️  SSH client not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️  SSH client not found")
        print("   On Windows: Enable OpenSSH client in Optional Features")
        print("   On Linux: Install openssh-client package")
        print("   Alternative: Use Docker deployment")
        return False

def test_slurm_manager():
    """Test SLURM manager functionality"""
    print("\nTesting SLURM manager...")
    try:
        from slurm_manager import SlurmManager
        slurm = SlurmManager()

        print(f"✅ SLURM manager initialized")
        print(f"   - Host: {slurm.slurm_host}")
        print(f"   - User: {slurm.config.SLURM_USER}")
        print(f"   - SSH mode: {slurm.use_ssh}")

        # Test basic command execution (only if local)
        if not slurm.use_ssh:
            result = slurm.run_command("echo 'test'")
            if result == 'test':
                print("✅ Local command execution working")
            else:
                print("⚠️  Local command execution failed")
        else:
            print("ℹ️  SSH mode - skipping local command test")

        # Test SLURM commands (may fail if not on SLURM system)
        try:
            if not slurm.use_ssh:
                queue_jobs = slurm.get_queue_jobs()
                print(f"✅ Queue jobs retrieval working (found {len(queue_jobs)} jobs)")
            else:
                print("ℹ️  SSH mode - SLURM commands will be tested when connecting to cluster")
        except Exception as e:
            print(f"⚠️  Queue jobs retrieval failed (expected if not on SLURM system): {e}")

        return True
    except Exception as e:
        print(f"❌ SLURM manager error: {e}")
        return False

def main():
    """Run all tests"""
    print("SLURM Dashboard Setup Test")
    print("=" * 50)

    tests = [
        test_python_version,
        test_dependencies,
        test_config,
        test_ssh_client,
        test_slurm_manager,
        test_slurm_commands
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 50)
    print(f"Test Results: {passed}/{total} passed")

    if passed == total:
        print("🎉 All tests passed! The dashboard should work correctly.")
        print("\nTo start the dashboard, run:")
        print("  python main.py")
        print("  or")
        print("  ./start_dashboard.sh (Linux/Mac)")
        print("  or")
        print("  start_dashboard.bat (Windows)")
    else:
        print("⚠️  Some tests failed. Please address the issues above.")
        if passed >= 3:  # Core functionality works
            print("Note: The dashboard may still work with limited functionality.")

if __name__ == '__main__':
    main()
