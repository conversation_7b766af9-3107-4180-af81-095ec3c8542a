"""
SLURM Manager - Interface for SLURM commands
Supports both local and remote (SSH) execution
"""
import subprocess
import json
import re
import os
import platform
import tempfile
from datetime import datetime
from config import Config

class SlurmManager:
    def __init__(self):
        self.config = Config()
        self.slurm_host = os.environ.get('SLURM_HOST', 'localhost')
        self.ssh_password = os.environ.get('SLURM_SSH_PASSWORD', '')
        self.use_ssh = self.slurm_host != 'localhost' and self.slurm_host != '127.0.0.1'

        if self.use_ssh:
            print(f"Using SSH connection to SLURM host: {self.slurm_host}")
            if not self.ssh_password:
                print("Warning: No SSH password provided. Set SLURM_SSH_PASSWORD environment variable.")
        else:
            print("Using local SLURM commands")

    def run_command(self, command, use_sudo=False):
        """Execute SLURM command with optional sudo (local or remote via SSH)"""
        try:
            if use_sudo:
                command = f"sudo {command}"

            if self.use_ssh:
                # Execute command via SSH using Paramiko
                result = self._execute_ssh_command(command)

                if result is None:
                    print(f"SSH execution failed for command: {command}")
                    return None
            else:
                # Execute command locally
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=30
                )

            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"Command failed: {command}")
                if hasattr(result, 'stderr') and result.stderr:
                    print(f"Error: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            print(f"Command timed out: {command}")
            return None
        except Exception as e:
            print(f"Error executing command: {e}")
            return None

    def _execute_ssh_command(self, command):
        """Execute SSH command using Paramiko - Simple and Fast"""
        ssh = None
        try:
            import paramiko

            # Create SSH client with optimized settings
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Quick connection with minimal timeout
            ssh.connect(
                hostname=self.slurm_host,
                username=self.config.SLURM_USER,
                password=self.ssh_password,
                timeout=3,  # Very short timeout for quick response
                allow_agent=False,
                look_for_keys=False,
                banner_timeout=3
            )

            # Execute command with timeout
            stdin, stdout, stderr = ssh.exec_command(command)

            # Set channel timeout for faster response
            stdout.channel.settimeout(5.0)
            stderr.channel.settimeout(5.0)

            # Get results quickly
            exit_status = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8', errors='ignore')
            stderr_data = stderr.read().decode('utf-8', errors='ignore')

            # Create simple result object
            class SSHResult:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            return SSHResult(exit_status, stdout_data, stderr_data)

        except paramiko.AuthenticationException:
            print(f"SSH Authentication failed for {self.config.SLURM_USER}@{self.slurm_host}")
            print(f"  Check username/password for host {self.slurm_host}")
            return None
        except paramiko.SSHException as e:
            print(f"SSH connection error to {self.slurm_host}: {e}")
            return None
        except Exception as e:
            print(f"SSH error to {self.slurm_host}: {e}")
            print(f"  Host: {self.slurm_host}")
            print(f"  User: {self.config.SLURM_USER}")
            print(f"  Password set: {'Yes' if self.ssh_password else 'No'}")
            return None
        finally:
            # Always close connection
            if ssh:
                try:
                    ssh.close()
                except:
                    pass





    def get_queue_jobs(self):
        """Get queue jobs grouped by base job ID to avoid duplicates"""
        # Use the detailed version for consistency
        return self.get_queue_jobs_with_details()

    def get_queue_jobs_with_details(self):
        """Get queue jobs grouped by base job ID for clean display"""
        command = "squeue -t PD -o '%i|%j|%u|%T|%P|%Q|%r|%S' --noheader"
        output = self.run_command(command)

        if not output:
            return []

        # Group jobs by base job ID
        job_groups = {}
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 8:
                    full_job_id = parts[0].strip()
                    base_job_id = full_job_id.split('_')[0]

                    job_data = {
                        'full_job_id': full_job_id,
                        'name': parts[1].strip(),
                        'user': parts[2].strip(),
                        'state': parts[3].strip(),
                        'partition': parts[4].strip(),
                        'priority': parts[5].strip(),
                        'reason': parts[6].strip(),
                        'submit_time': parts[7].strip()
                    }

                    if base_job_id not in job_groups:
                        job_groups[base_job_id] = {
                            'base_job_id': base_job_id,
                            'jobs': [],
                            'name': job_data['name'],
                            'user': job_data['user'],
                            'partition': job_data['partition'],
                            'priority': job_data['priority'],
                            'reason': job_data['reason'],
                            'submit_time': job_data['submit_time']
                        }

                    job_groups[base_job_id]['jobs'].append(job_data)

                    # Use highest priority if multiple priorities exist
                    current_priority = int(job_groups[base_job_id]['priority']) if job_groups[base_job_id]['priority'].isdigit() else 0
                    new_priority = int(job_data['priority']) if job_data['priority'].isdigit() else 0
                    if new_priority > current_priority:
                        job_groups[base_job_id]['priority'] = job_data['priority']

        # Convert grouped jobs to display format
        consolidated_jobs = []
        for base_job_id, group in job_groups.items():
            job_count = len(group['jobs'])

            # Create job range display
            if job_count == 1:
                job_display = base_job_id
                range_display = "1 job"
            else:
                # Extract job numbers for range display
                job_numbers = []
                for job in group['jobs']:
                    if '_' in job['full_job_id']:
                        try:
                            job_num = job['full_job_id'].split('_')[1]
                            job_numbers.append(int(job_num))
                        except:
                            pass

                if job_numbers:
                    job_numbers.sort()
                    if len(job_numbers) > 1 and job_numbers[-1] - job_numbers[0] == len(job_numbers) - 1:
                        # Consecutive range
                        range_display = f"[{job_numbers[0]}-{job_numbers[-1]}]"
                    else:
                        # Non-consecutive, show count
                        range_display = f"[{job_count} jobs]"
                    job_display = f"{base_job_id} {range_display}"
                else:
                    job_display = f"{base_job_id} [{job_count} jobs]"

            consolidated_job = {
                'job_id': base_job_id,  # Base job ID for operations
                'display_job_id': job_display,  # Display with range
                'name': group['name'],
                'user': group['user'],
                'state': 'PD',
                'partition': group['partition'],
                'priority': group['priority'],
                'reason': group['reason'],
                'submit_time': group['submit_time'],
                'job_count': job_count,
                'individual_jobs': [job['full_job_id'] for job in group['jobs']]
            }
            consolidated_jobs.append(consolidated_job)

        # Sort by priority (descending)
        consolidated_jobs.sort(key=lambda x: int(x['priority']) if x['priority'].isdigit() else 0, reverse=True)
        return consolidated_jobs

    def get_running_jobs(self):
        """Get currently running jobs with requeue support"""
        # Use the detailed version for consistency
        return self.get_running_jobs_with_details()

    def get_cluster_info(self):
        """Get cluster information"""
        info = {
            'name': self.config.CLUSTER_NAME,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Get node information
        node_command = "sinfo -N -o '%N|%T|%c|%m|%f' --noheader"
        node_output = self.run_command(node_command)

        nodes = {'total': 0, 'idle': 0, 'allocated': 0, 'down': 0, 'mixed': 0}
        total_cpus = 0
        total_memory = 0

        if node_output:
            for line in node_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 5:
                        state = parts[1].strip().lower()
                        cpus = parts[2].strip()
                        memory = parts[3].strip()

                        nodes['total'] += 1
                        if 'idle' in state:
                            nodes['idle'] += 1
                        elif 'alloc' in state:
                            nodes['allocated'] += 1
                        elif 'down' in state:
                            nodes['down'] += 1
                        elif 'mix' in state:
                            nodes['mixed'] += 1

                        if cpus.isdigit():
                            total_cpus += int(cpus)

                        # Extract memory value (remove units)
                        memory_match = re.search(r'(\d+)', memory)
                        if memory_match:
                            total_memory += int(memory_match.group(1))

        info['nodes'] = nodes
        info['total_cpus'] = total_cpus
        info['total_memory_gb'] = total_memory // 1024 if total_memory > 0 else 0

        # Get partition information
        partition_command = "sinfo -o '%P|%a|%l|%N' --noheader"
        partition_output = self.run_command(partition_command)

        partitions = []
        if partition_output:
            for line in partition_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 4:
                        partition = {
                            'name': parts[0].strip().rstrip('*'),
                            'availability': parts[1].strip(),
                            'time_limit': parts[2].strip(),
                            'nodes': parts[3].strip()
                        }
                        partitions.append(partition)

        info['partitions'] = partitions

        return info

    def change_job_priority(self, job_id, new_priority):
        """Change job priority using scontrol"""
        command = f"scontrol update JobId={job_id} Priority={new_priority}"

        # 1) Try without sudo first (most clusters allow privilege via SLURM_USER)
        result = self.run_command(command)

        # 2) If the command failed (None) try again with sudo as a fallback.
        #    This ensures we don't blindly prepend sudo for every priority change.
        if result is None:
            result = self.run_command(command, use_sudo=True)

        return result is not None

    def get_job_details(self, job_id):
        """Get detailed information about a specific job"""
        command = f"scontrol show job {job_id}"
        output = self.run_command(command)

        if not output:
            return None

        # Parse job details
        details = {}
        for line in output.split('\n'):
            if '=' in line:
                parts = line.split('=', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    details[key] = value

        return details

    def requeue_job(self, base_job_id):
        """Requeue running jobs: 1) Move to bottom, 2) Requeue individual jobs, 3) Release individual jobs"""
        print(f"DEBUG: Requeuing job with base_job_id: {base_job_id}")

        # Get all individual job IDs for this base job ID
        individual_job_ids = self.get_individual_job_ids(base_job_id)
        print(f"DEBUG: Found individual job IDs: {individual_job_ids}")

        if not individual_job_ids:
            return False, f"No running jobs found for job ID {base_job_id}"

        import time

        # Step 1: Move to bottom of queue FIRST (so it doesn't affect new runs)
        print(f"DEBUG: Step 1 - Moving {base_job_id} to bottom of queue BEFORE requeue")
        bottom_priority_result = self.set_bottom_priority(base_job_id)
        if not bottom_priority_result[0]:
            print(f"DEBUG: Warning - Failed to move to bottom: {bottom_priority_result[1]}")
        else:
            print(f"DEBUG: Bottom priority result: {bottom_priority_result[1]}")

        # Wait for queue reordering to take effect
        time.sleep(2)

        # Step 2: Requeue using individual job IDs
        job_ids_str = ' '.join(individual_job_ids)
        requeue_command = f"scontrol requeue {job_ids_str}"
        print(f"DEBUG: Step 2 - Executing requeue command: {requeue_command}")
        requeue_result = self.run_command(requeue_command)

        if requeue_result is None:
            # Try with sudo if direct command failed
            print(f"DEBUG: Requeue failed, trying with sudo")
            requeue_result = self.run_command(requeue_command, use_sudo=True)

        if requeue_result is None:
            return False, f"Failed to requeue jobs {job_ids_str}"

        print(f"DEBUG: Requeue result: {requeue_result}")

        # Wait a moment for the requeue to process
        time.sleep(2)

        # Step 3: Release using individual job IDs
        release_command = f"scontrol release {job_ids_str}"
        print(f"DEBUG: Step 3 - Executing release command: {release_command}")
        release_result = self.run_command(release_command)

        if release_result is None:
            # Try with sudo if direct command failed
            print(f"DEBUG: Release failed, trying with sudo")
            release_result = self.run_command(release_command, use_sudo=True)

        if release_result is None:
            return False, f"Jobs requeued but failed to release: {job_ids_str}"

        print(f"DEBUG: Release result: {release_result}")

        job_count = len(individual_job_ids)
        return True, f"Successfully moved to bottom, requeued, and released {job_count} jobs for {base_job_id}"

    def cancel_job(self, base_job_id):
        """Cancel jobs using scancel with base job ID only"""
        print(f"DEBUG: Canceling job with base_job_id: {base_job_id}")

        # Use base job ID only - scancel will handle all array elements automatically
        cancel_command = f"scancel {base_job_id}"
        print(f"DEBUG: Executing cancel command: {cancel_command}")
        cancel_result = self.run_command(cancel_command)

        if cancel_result is None:
            # Try with sudo if direct command failed
            print(f"DEBUG: Cancel failed, trying with sudo")
            cancel_result = self.run_command(cancel_command, use_sudo=True)

        if cancel_result is None:
            return False, f"Failed to cancel job {base_job_id}"

        print(f"DEBUG: Cancel result: {cancel_result}")

        return True, f"Successfully canceled job {base_job_id} and all its array elements"

    def check_job_states(self, base_job_id):
        """Check the current states of all jobs for a given base job ID"""
        # Get all jobs (running and pending) for this base job ID
        running_command = "squeue -t R -o '%i|%T' --noheader"
        pending_command = "squeue -t PD -o '%i|%T' --noheader"

        states = {'running': 0, 'pending': 0, 'other': 0}

        # Check running jobs
        running_output = self.run_command(running_command)
        if running_output:
            for line in running_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 2:
                        job_id = parts[0].strip()
                        if job_id.startswith(base_job_id + '_') or job_id == base_job_id:
                            states['running'] += 1

        # Check pending jobs
        pending_output = self.run_command(pending_command)
        if pending_output:
            for line in pending_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 2:
                        job_id = parts[0].strip()
                        if job_id.startswith(base_job_id + '_') or job_id == base_job_id:
                            states['pending'] += 1

        return states

    def set_bottom_priority(self, base_job_id):
        """Move job to bottom of queue by setting lowest priority"""
        print(f"DEBUG: Moving job {base_job_id} to bottom of queue")

        # Get current queue jobs to find lowest priority
        queue_jobs = self.get_queue_jobs_with_details()

        if not queue_jobs:
            print(f"DEBUG: No jobs in queue, setting default low priority")
            # Set a low priority for empty queue
            return self.change_job_priority(base_job_id, 1)

        # Find the lowest priority among existing jobs (excluding target job)
        priorities = []
        for job in queue_jobs:
            if job['job_id'] != base_job_id and job['priority'].isdigit():
                priorities.append(int(job['priority']))

        if priorities:
            # Set priority lower than the current lowest
            lowest_priority = min(priorities)
            new_priority = max(1, lowest_priority - 1)  # Ensure minimum priority is 1
            print(f"DEBUG: Setting priority to {new_priority} (lower than current lowest {lowest_priority})")
        else:
            # No other jobs or no valid priorities, set to 1
            new_priority = 1
            print(f"DEBUG: No other jobs with valid priorities, setting to {new_priority}")

        # Use the working priority change method
        success = self.change_job_priority(base_job_id, new_priority)

        if success:
            return True, f"Set job {base_job_id} to bottom of queue with priority {new_priority}"
        else:
            return False, f"Failed to set priority for job {base_job_id}"

    def get_individual_job_ids(self, base_job_id):
        """Get all individual job IDs for a base job ID from running jobs"""
        command = "squeue -t R -o '%i' --noheader"
        output = self.run_command(command)
        print(f"DEBUG: squeue output for individual jobs: {output}")

        if not output:
            return []

        individual_jobs = []
        for line in output.split('\n'):
            if line.strip():
                job_id = line.strip()
                print(f"DEBUG: Checking job_id '{job_id}' against base_job_id '{base_job_id}'")
                # Check if this job belongs to the base job ID
                if job_id.startswith(base_job_id + '_') or job_id == base_job_id:
                    individual_jobs.append(job_id)
                    print(f"DEBUG: Added job_id '{job_id}' to individual_jobs")

        print(f"DEBUG: Final individual_jobs list: {individual_jobs}")
        return individual_jobs

    def get_running_jobs_with_details(self):
        """Get running jobs grouped by base job ID for requeue functionality"""
        # Get basic running jobs info
        command = "squeue -t R -o '%i|%j|%u|%T|%P|%M|%N|%l' --noheader"
        output = self.run_command(command)

        if not output:
            return []

        # Group jobs by base job ID
        job_groups = {}
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 7:
                    full_job_id = parts[0].strip()
                    base_job_id = full_job_id.split('_')[0]

                    job_data = {
                        'full_job_id': full_job_id,
                        'name': parts[1].strip(),
                        'user': parts[2].strip(),
                        'state': parts[3].strip(),
                        'partition': parts[4].strip(),
                        'time': parts[5].strip(),
                        'nodes': parts[6].strip(),
                        'time_limit': parts[7].strip() if len(parts) > 7 else 'N/A'
                    }

                    if base_job_id not in job_groups:
                        job_groups[base_job_id] = {
                            'base_job_id': base_job_id,
                            'jobs': [],
                            'name': job_data['name'],
                            'user': job_data['user'],
                            'partition': job_data['partition'],
                            'time_limit': job_data['time_limit']
                        }

                    job_groups[base_job_id]['jobs'].append(job_data)

        # Convert grouped jobs to display format
        consolidated_jobs = []
        for base_job_id, group in job_groups.items():
            job_count = len(group['jobs'])

            # Create job range display
            if job_count == 1:
                job_display = base_job_id
                range_display = "1 job"
            else:
                # Extract job numbers for range display
                job_numbers = []
                for job in group['jobs']:
                    if '_' in job['full_job_id']:
                        try:
                            job_num = job['full_job_id'].split('_')[1]
                            job_numbers.append(int(job_num))
                        except:
                            pass

                if job_numbers:
                    job_numbers.sort()
                    if len(job_numbers) > 1 and job_numbers[-1] - job_numbers[0] == len(job_numbers) - 1:
                        # Consecutive range
                        range_display = f"[{job_numbers[0]}-{job_numbers[-1]}]"
                    else:
                        # Non-consecutive, show count
                        range_display = f"[{job_count} jobs]"
                    job_display = f"{base_job_id} {range_display}"
                else:
                    job_display = f"{base_job_id} [{job_count} jobs]"

            # Get representative runtime (use first job's time)
            runtime = group['jobs'][0]['time'] if group['jobs'] else 'N/A'

            # Get nodes info (combine or use first)
            nodes_list = [job['nodes'] for job in group['jobs'] if job['nodes']]
            nodes_display = f"{len(nodes_list)} nodes" if len(nodes_list) > 1 else (nodes_list[0] if nodes_list else 'N/A')

            consolidated_job = {
                'job_id': base_job_id,  # Base job ID for requeue operations
                'display_job_id': job_display,  # Display with range
                'name': group['name'],
                'user': group['user'],
                'state': 'R',
                'partition': group['partition'],
                'time': runtime,
                'nodes': nodes_display,
                'time_limit': group['time_limit'],
                'job_count': job_count,
                'individual_jobs': [job['full_job_id'] for job in group['jobs']]
            }
            consolidated_jobs.append(consolidated_job)

        return consolidated_jobs
