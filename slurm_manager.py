"""
SLURM Manager - Interface for SLURM commands
Supports both local and remote (SSH) execution
"""
import subprocess
import json
import re
import os
import platform
import tempfile
from datetime import datetime
from config import Config

class SlurmManager:
    def __init__(self):
        self.config = Config()
        self.slurm_host = os.environ.get('SLURM_HOST', 'localhost')
        self.ssh_password = os.environ.get('SLURM_SSH_PASSWORD', '')
        self.use_ssh = self.slurm_host != 'localhost' and self.slurm_host != '127.0.0.1'

        if self.use_ssh:
            print(f"Using SSH connection to SLURM host: {self.slurm_host}")
            if not self.ssh_password:
                print("Warning: No SSH password provided. Set SLURM_SSH_PASSWORD environment variable.")
        else:
            print("Using local SLURM commands")

    def run_command(self, command, use_sudo=False):
        """Execute SLURM command with optional sudo (local or remote via SSH)"""
        try:
            if use_sudo:
                command = f"sudo -u {self.config.SLURM_USER} {command}"

            if self.use_ssh:
                # Execute command via SSH using Paramiko
                result = self._execute_ssh_command(command)

                if result is None:
                    print(f"SSH execution failed for command: {command}")
                    return None
            else:
                # Execute command locally
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=30
                )

            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"Command failed: {command}")
                if hasattr(result, 'stderr') and result.stderr:
                    print(f"Error: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            print(f"Command timed out: {command}")
            return None
        except Exception as e:
            print(f"Error executing command: {e}")
            return None

    def _execute_ssh_command(self, command):
        """Execute SSH command using Paramiko - Simple and Fast"""
        ssh = None
        try:
            import paramiko

            # Create SSH client with optimized settings
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Quick connection with minimal timeout
            ssh.connect(
                hostname=self.slurm_host,
                username=self.config.SLURM_USER,
                password=self.ssh_password,
                timeout=3,  # Very short timeout for quick response
                allow_agent=False,
                look_for_keys=False,
                banner_timeout=3
            )

            # Execute command with timeout
            stdin, stdout, stderr = ssh.exec_command(command)

            # Set channel timeout for faster response
            stdout.channel.settimeout(5.0)
            stderr.channel.settimeout(5.0)

            # Get results quickly
            exit_status = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8', errors='ignore')
            stderr_data = stderr.read().decode('utf-8', errors='ignore')

            # Create simple result object
            class SSHResult:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            return SSHResult(exit_status, stdout_data, stderr_data)

        except paramiko.AuthenticationException:
            print(f"SSH Authentication failed for {self.config.SLURM_USER}@{self.slurm_host}")
            print(f"  Check username/password for host {self.slurm_host}")
            return None
        except paramiko.SSHException as e:
            print(f"SSH connection error to {self.slurm_host}: {e}")
            return None
        except Exception as e:
            print(f"SSH error to {self.slurm_host}: {e}")
            print(f"  Host: {self.slurm_host}")
            print(f"  User: {self.config.SLURM_USER}")
            print(f"  Password set: {'Yes' if self.ssh_password else 'No'}")
            return None
        finally:
            # Always close connection
            if ssh:
                try:
                    ssh.close()
                except:
                    pass





    def get_queue_jobs(self):
        """Get all jobs in queue (pending state)"""
        command = "squeue -t PD -o '%i|%j|%u|%T|%P|%Q|%r|%S' --noheader"
        output = self.run_command(command)

        if not output:
            return []

        jobs = []
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 8:
                    job = {
                        'job_id': parts[0].strip(),
                        'name': parts[1].strip(),
                        'user': parts[2].strip(),
                        'state': parts[3].strip(),
                        'partition': parts[4].strip(),
                        'priority': parts[5].strip(),
                        'reason': parts[6].strip(),
                        'submit_time': parts[7].strip()
                    }
                    jobs.append(job)

        # Sort by priority (descending)
        jobs.sort(key=lambda x: int(x['priority']) if x['priority'].isdigit() else 0, reverse=True)
        return jobs[:self.config.MAX_QUEUE_JOBS]

    def get_running_jobs(self):
        """Get currently running jobs"""
        command = "squeue -t R -o '%i|%j|%u|%T|%P|%M|%N' --noheader"
        output = self.run_command(command)

        if not output:
            return []

        jobs = []
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 7:
                    job = {
                        'job_id': parts[0].strip(),
                        'name': parts[1].strip(),
                        'user': parts[2].strip(),
                        'state': parts[3].strip(),
                        'partition': parts[4].strip(),
                        'time': parts[5].strip(),
                        'nodes': parts[6].strip()
                    }
                    jobs.append(job)

        return jobs  # Removed limit to show all running jobs

    def get_cluster_info(self):
        """Get cluster information"""
        info = {
            'name': self.config.CLUSTER_NAME,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Get node information
        node_command = "sinfo -N -o '%N|%T|%c|%m|%f' --noheader"
        node_output = self.run_command(node_command)

        nodes = {'total': 0, 'idle': 0, 'allocated': 0, 'down': 0, 'mixed': 0}
        total_cpus = 0
        total_memory = 0

        if node_output:
            for line in node_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 5:
                        state = parts[1].strip().lower()
                        cpus = parts[2].strip()
                        memory = parts[3].strip()

                        nodes['total'] += 1
                        if 'idle' in state:
                            nodes['idle'] += 1
                        elif 'alloc' in state:
                            nodes['allocated'] += 1
                        elif 'down' in state:
                            nodes['down'] += 1
                        elif 'mix' in state:
                            nodes['mixed'] += 1

                        if cpus.isdigit():
                            total_cpus += int(cpus)

                        # Extract memory value (remove units)
                        memory_match = re.search(r'(\d+)', memory)
                        if memory_match:
                            total_memory += int(memory_match.group(1))

        info['nodes'] = nodes
        info['total_cpus'] = total_cpus
        info['total_memory_gb'] = total_memory // 1024 if total_memory > 0 else 0

        # Get partition information
        partition_command = "sinfo -o '%P|%a|%l|%N' --noheader"
        partition_output = self.run_command(partition_command)

        partitions = []
        if partition_output:
            for line in partition_output.split('\n'):
                if line.strip():
                    parts = line.split('|')
                    if len(parts) >= 4:
                        partition = {
                            'name': parts[0].strip().rstrip('*'),
                            'availability': parts[1].strip(),
                            'time_limit': parts[2].strip(),
                            'nodes': parts[3].strip()
                        }
                        partitions.append(partition)

        info['partitions'] = partitions

        return info

    def change_job_priority(self, job_id, new_priority):
        """Change job priority using scontrol"""
        command = f"scontrol update JobId={job_id} Priority={new_priority}"

        # 1) Try without sudo first (most clusters allow privilege via SLURM_USER)
        result = self.run_command(command)

        # 2) If the command failed (None) try again with sudo as a fallback.
        #    This ensures we don't blindly prepend sudo for every priority change.
        if result is None:
            result = self.run_command(command, use_sudo=True)

        return result is not None

    def get_job_details(self, job_id):
        """Get detailed information about a specific job"""
        command = f"scontrol show job {job_id}"
        output = self.run_command(command)

        if not output:
            return None

        # Parse job details
        details = {}
        for line in output.split('\n'):
            if '=' in line:
                parts = line.split('=', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    details[key] = value

        return details
