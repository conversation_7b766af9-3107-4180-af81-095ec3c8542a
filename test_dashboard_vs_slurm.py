#!/usr/bin/env python3
"""
Test what the dashboard is sending vs what SLURM is actually doing
"""
import requests
import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cluster_manager import ClusterManager

def test_dashboard_vs_slurm():
    """Test dashboard vs SLURM reality"""
    
    print("TESTING DASHBOARD VS SLURM REALITY")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Get cluster info
    response = requests.get(f"{base_url}/api/clusters", timeout=5)
    clusters = response.json()
    
    if not clusters:
        print("❌ No clusters found")
        return
        
    cluster_id = clusters[0]['id']
    print(f"✅ Using cluster: {clusters[0]['name']} ({cluster_id})")
    
    # Get direct SLURM data
    cluster_manager = ClusterManager()
    manager = cluster_manager.clusters[cluster_id]
    
    print(f"\n1. DIRECT SLURM QUEUE:")
    slurm_queue = manager.get_queue_jobs()
    for i, job in enumerate(slurm_queue):
        short_id = job['job_id'].split('_')[0]
        print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')})")
    
    # Get dashboard API data
    print(f"\n2. DASHBOARD API DATA:")
    response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
    if response.status_code == 200:
        dashboard_queue = response.json()
        for i, job in enumerate(dashboard_queue):
            short_id = job['job_id'].split('_')[0]
            print(f"   {i+1}. Job {short_id} (Priority: {job.get('priority', 'N/A')})")
    else:
        print(f"   ❌ Dashboard API failed: {response.status_code}")
        return
    
    # Compare the two
    print(f"\n3. COMPARISON:")
    if len(slurm_queue) != len(dashboard_queue):
        print(f"   ❌ Different number of jobs: SLURM={len(slurm_queue)}, Dashboard={len(dashboard_queue)}")
    else:
        print(f"   ✅ Same number of jobs: {len(slurm_queue)}")
    
    # Check order
    slurm_order = [job['job_id'] for job in slurm_queue]
    dashboard_order = [job['job_id'] for job in dashboard_queue]
    
    if slurm_order == dashboard_order:
        print(f"   ✅ Order matches between SLURM and Dashboard")
    else:
        print(f"   ❌ Order MISMATCH:")
        print(f"      SLURM:     {[job_id.split('_')[0] for job_id in slurm_order]}")
        print(f"      Dashboard: {[job_id.split('_')[0] for job_id in dashboard_order]}")
    
    # Test a simple reorder through the API
    if len(dashboard_queue) >= 2:
        print(f"\n4. TESTING API REORDER:")
        
        # Test moving last job to second position
        last_job = dashboard_queue[-1]
        last_job_id = last_job['job_id']
        last_job_short = last_job_id.split('_')[0]
        
        print(f"   Testing: Move job {last_job_short} to top priority")
        
        # Record current order
        before_order = [job['job_id'] for job in manager.get_queue_jobs()]
        print(f"   Before: {[job_id.split('_')[0] for job_id in before_order]}")
        
        # Execute API call
        response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                               json={'job_id': last_job_id}, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API Response: {result}")
            
            # Wait and check result
            time.sleep(2)
            after_order = [job['job_id'] for job in manager.get_queue_jobs()]
            print(f"   After:  {[job_id.split('_')[0] for job_id in after_order]}")
            
            # Check if job moved to top
            if after_order[0] == last_job_id:
                print(f"   ✅ SUCCESS: Job {last_job_short} moved to top")
            else:
                print(f"   ❌ FAILED: Job {last_job_short} not at top")
                
                # Find where it ended up
                try:
                    new_position = after_order.index(last_job_id) + 1
                    print(f"   Job {last_job_short} is at position {new_position}")
                except ValueError:
                    print(f"   Job {last_job_short} not found in queue!")
        else:
            print(f"   ❌ API call failed: {response.status_code}")
    
    print(f"\n5. DASHBOARD DEBUGGING TIPS:")
    print(f"   When you drag and drop in the dashboard:")
    print(f"   ")
    print(f"   1. Open browser console (F12 → Console)")
    print(f"   2. Look for these messages:")
    print(f"      - '🖱️ DRAG COMPLETED: Job X moved from position Y to Z'")
    print(f"      - '🧠 MULTI-JOB REORDERING: Applying precise order changes'")
    print(f"      - '🎯 Using REVERSE ORDER approach'")
    print(f"      - 'Commands will be executed in this order:'")
    print(f"      - '🔄 Step 1/X: scontrol top ...'")
    print(f"   ")
    print(f"   3. If you don't see these messages:")
    print(f"      - JavaScript might have errors")
    print(f"      - Drag detection might not be working")
    print(f"      - Save button might not be triggering")
    print(f"   ")
    print(f"   4. If you see the messages but wrong final position:")
    print(f"      - Check if all API calls return success")
    print(f"      - Check if dashboard refreshes after save")
    print(f"      - Try manual refresh (F5) after save")
    
    print(f"\n6. NEXT STEPS:")
    print(f"   The SLURM backend is working correctly (proven by tests)")
    print(f"   The issue is likely in:")
    print(f"   - Dashboard JavaScript execution")
    print(f"   - Timing between save and refresh")
    print(f"   - Browser console errors")
    print(f"   ")
    print(f"   Please check browser console during your next drag test!")

if __name__ == "__main__":
    test_dashboard_vs_slurm()
