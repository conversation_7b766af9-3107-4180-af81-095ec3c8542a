#!/usr/bin/env python3
"""
Debug the job count issues
"""
import requests

def debug_job_counts():
    """Debug the job count and reordering issues"""
    
    base_url = "http://localhost:5000"
    
    print("Debugging Job Count and Reordering Issues...")
    print("=" * 60)
    
    try:
        # Get cluster info
        response = requests.get(f"{base_url}/api/clusters", timeout=5)
        clusters = response.json()
        
        if not clusters:
            print("❌ No clusters found")
            return
            
        cluster_id = clusters[0]['id']
        cluster = clusters[0]
        
        print(f"✅ Cluster: {cluster['name']} ({cluster_id})")
        print(f"   Host: {cluster['host']}")
        print(f"   Dashboard shows: {cluster['running_count']} running, {cluster['queue_count']} queued")
        
        # Get detailed data
        print(f"\n🔍 Fetching detailed cluster data...")
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/data", timeout=15)
        
        if response.status_code != 200:
            print(f"❌ Failed to get cluster data: {response.status_code}")
            print(f"Response: {response.text}")
            return
            
        data = response.json()
        
        if 'error' in data:
            print(f"❌ Cluster data error: {data['error']}")
            return
        
        # Check job counts
        running_jobs = data.get('running_jobs', [])
        queue_jobs = data.get('queue_jobs', [])
        
        print(f"\n📊 ACTUAL JOB COUNTS:")
        print(f"   Running jobs: {len(running_jobs)}")
        print(f"   Queue jobs: {len(queue_jobs)}")
        print(f"   Total jobs: {len(running_jobs) + len(queue_jobs)}")
        
        # Show sample running jobs
        if running_jobs:
            print(f"\n🏃 SAMPLE RUNNING JOBS (first 10):")
            for i, job in enumerate(running_jobs[:10]):
                print(f"   {i+1}. Job {job.get('job_id', 'N/A')}: {job.get('name', 'N/A')} on {job.get('nodes', 'N/A')}")
            
            if len(running_jobs) > 10:
                print(f"   ... and {len(running_jobs) - 10} more running jobs")
        
        # Show queue jobs
        if queue_jobs:
            print(f"\n⏳ QUEUE JOBS:")
            for i, job in enumerate(queue_jobs):
                priority = job.get('priority', 'N/A')
                print(f"   {i+1}. Job {job.get('job_id', 'N/A')}: {job.get('name', 'N/A')} (Priority: {priority})")
        
        # Check cluster info
        cluster_info = data.get('cluster_info', {})
        if cluster_info:
            print(f"\n🖥️ CLUSTER INFO:")
            nodes = cluster_info.get('nodes', {})
            print(f"   Total nodes: {nodes.get('total', 'N/A')}")
            print(f"   Idle nodes: {nodes.get('idle', 'N/A')}")
            print(f"   Allocated nodes: {nodes.get('allocated', 'N/A')}")
            print(f"   Mixed nodes: {nodes.get('mixed', 'N/A')}")
            print(f"   Total CPUs: {cluster_info.get('total_cpus', 'N/A')}")
        
        # Test individual endpoints
        print(f"\n🔍 TESTING INDIVIDUAL ENDPOINTS:")
        
        # Test running jobs endpoint
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/running_jobs", timeout=10)
        if response.status_code == 200:
            running_direct = response.json()
            print(f"   /running_jobs: {len(running_direct)} jobs")
        else:
            print(f"   /running_jobs: ERROR {response.status_code}")
        
        # Test queue jobs endpoint
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/queue_jobs", timeout=10)
        if response.status_code == 200:
            queue_direct = response.json()
            print(f"   /queue_jobs: {len(queue_direct)} jobs")
        else:
            print(f"   /queue_jobs: ERROR {response.status_code}")
        
        # Test cluster info endpoint
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/info", timeout=10)
        if response.status_code == 200:
            info_direct = response.json()
            print(f"   /info: {info_direct}")
        else:
            print(f"   /info: ERROR {response.status_code}")
        
        # Test reordering endpoint
        if queue_jobs:
            print(f"\n🔄 TESTING REORDERING ENDPOINT:")
            test_job = queue_jobs[0]['job_id']
            print(f"   Testing with job: {test_job}")
            
            response = requests.post(f"{base_url}/api/cluster/{cluster_id}/set_top_priority", 
                                   json={'job_id': test_job}, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ Reordering works: {result.get('message', 'Success')}")
                else:
                    print(f"   ❌ Reordering failed: {result.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ Reordering HTTP error: {response.status_code}")
                print(f"   Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_job_counts()
