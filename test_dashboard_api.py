#!/usr/bin/env python3
"""
Test dashboard API endpoints
"""
import requests
import json

def test_api_endpoints():
    """Test the dashboard API endpoints"""
    
    base_url = "http://localhost:5000"
    
    print("Testing Dashboard API Endpoints...")
    print("=" * 50)
    
    # Test 1: Get clusters list
    print("1. Testing /api/clusters...")
    try:
        response = requests.get(f"{base_url}/api/clusters", timeout=10)
        if response.status_code == 200:
            clusters = response.json()
            print(f"   ✅ Found {len(clusters)} clusters")
            for cluster in clusters:
                print(f"     - {cluster['id']}: {cluster['name']} ({cluster['status']})")
                cluster_id = cluster['id']
        else:
            print(f"   ❌ Error: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    if not clusters:
        print("   ❌ No clusters found in API")
        return
    
    # Use first cluster for testing
    cluster_id = clusters[0]['id']
    
    # Test 2: Get cluster data
    print(f"\n2. Testing /api/cluster/{cluster_id}/data...")
    try:
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/data", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Status: {data.get('status', 'unknown')}")
            print(f"   ✅ Queue jobs: {len(data.get('queue_jobs', []))}")
            print(f"   ✅ Running jobs: {len(data.get('running_jobs', []))}")
            print(f"   ✅ Last update: {data.get('last_update', 'never')}")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Get running jobs
    print(f"\n3. Testing /api/cluster/{cluster_id}/running_jobs...")
    try:
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/running_jobs", timeout=10)
        if response.status_code == 200:
            jobs = response.json()
            print(f"   ✅ Found {len(jobs)} running jobs")
            for job in jobs[:3]:
                print(f"     - Job {job['job_id']}: {job['name']}")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Get cluster info
    print(f"\n4. Testing /api/cluster/{cluster_id}/info...")
    try:
        response = requests.get(f"{base_url}/api/cluster/{cluster_id}/info", timeout=10)
        if response.status_code == 200:
            info = response.json()
            nodes = info.get('nodes', {})
            print(f"   ✅ Nodes: {nodes.get('total', 0)} total")
            print(f"   ✅ CPUs: {info.get('total_cpus', 0)}")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_api_endpoints()
