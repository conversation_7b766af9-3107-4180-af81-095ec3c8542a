#!/usr/bin/env python3
"""
Test the dashboard API connection endpoint
"""
import requests
import json

def test_api_connection():
    """Test the /api/test-connection endpoint"""
    
    url = "http://localhost:5000/api/test-connection"
    
    test_data = {
        "host": "************",
        "username": "nvme", 
        "password": "logan",
        "port": 22
    }
    
    print("Testing Dashboard API Connection...")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Host: {test_data['host']}")
    print(f"Username: {test_data['username']}")
    print()
    
    try:
        print("Sending test connection request...")
        response = requests.post(
            url,
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_data),
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Response Data:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print("\n✅ API Test Connection: SUCCESS")
                if result.get('slurm_version'):
                    print(f"   SLURM Version: {result['slurm_version']}")
                if result.get('message'):
                    print(f"   Message: {result['message']}")
            else:
                print("\n❌ API Test Connection: FAILED")
                print(f"   Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("\n❌ Request timed out (30s)")
    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to dashboard (is it running on localhost:5000?)")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    test_api_connection()
