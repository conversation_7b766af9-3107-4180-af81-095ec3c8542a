#!/bin/bash

# SLURM Dashboard Startup Script

echo "=========================================="
echo "       SLURM Dashboard Startup"
echo "=========================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not installed or not in PATH"
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing Python dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
else
    echo "Warning: requirements.txt not found"
fi

# Check if SLURM commands are available
echo "Checking SLURM installation..."
if ! command -v squeue &> /dev/null; then
    echo "Warning: squeue command not found. Make sure SLURM is installed and in PATH"
fi

if ! command -v sinfo &> /dev/null; then
    echo "Warning: sinfo command not found. Make sure SLURM is installed and in PATH"
fi

if ! command -v scontrol &> /dev/null; then
    echo "Warning: scontrol command not found. Make sure SLURM is installed and in PATH"
fi

# Set environment variables if not already set
export FLASK_APP=app.py
export FLASK_ENV=development

echo "Starting SLURM Dashboard..."
echo "Dashboard will be available at: http://localhost:5000"
echo "Press Ctrl+C to stop the server"
echo "=========================================="

# Start the application
python3 main.py
