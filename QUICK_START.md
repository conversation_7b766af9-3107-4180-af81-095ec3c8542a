# SLURM Dashboard - Quick Start Guide

## 🚀 Zero-Configuration Startup

The SLURM Dashboard is designed to work out-of-the-box with **no initial configuration required**. You add clusters dynamically through the web interface!

## 📋 Prerequisites

### Windows
- **Python 3.7+** from [python.org](https://python.org)
- **OpenSSH Client** (built into Windows 10+)

### Linux/macOS
- **Python 3.7+**
- **SSH client** (usually pre-installed)

### Docker (Any Platform)
- **Docker** and **Docker Compose**

## ⚡ 30-Second Setup

### Option 1: Direct Python (Windows/Linux/macOS)

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Start dashboard
python main.py

# 3. Open browser
# Go to: http://localhost:5000
```

### Option 2: Docker (Recommended)

```bash
# 1. Start with Docker
docker-compose up --build -d

# 2. Open browser
# Go to: http://localhost:5000
```

## 🎯 Adding Your First Cluster

1. **Open the dashboard** in your browser (`http://localhost:5000`)

2. **Click "Add Cluster"** button in the header

3. **Fill in cluster details:**
   - **Cluster Name**: `Production HPC` (or any name you prefer)
   - **Host/IP Address**: `slurm-head.company.com` (your SLURM head node)
   - **Username**: `slurmadmin` (your SSH username)
   - **Password**: `your-ssh-password` (your SSH password)
   - **SSH Port**: `22` (default, change if needed)
   - **Description**: `Main production cluster` (optional)

4. **Test Connection** (optional but recommended)
   - Click "Test Connection" to verify credentials
   - Wait for success confirmation

5. **Save Cluster**
   - Click "Save Cluster"
   - Cluster will be added and automatically selected

6. **Start Managing Jobs!**
   - View queued jobs in the left panel
   - Drag jobs up/down to change priority
   - Monitor running jobs in the main area
   - View cluster statistics

## 🔄 Adding More Clusters

Repeat the same process to add additional clusters:
- Development clusters
- Testing environments  
- Different HPC systems
- Remote clusters

**Switch between clusters** using the dropdown in the header.

## 🎛️ Dashboard Features

### Multi-Cluster Management
- **Dynamic Addition**: Add clusters without restarting
- **Easy Switching**: Dropdown selector for active cluster
- **Real-time Status**: Live cluster health indicators
- **Centralized View**: Manage all clusters from one interface

### Job Management
- **Queue Visualization**: See all pending jobs with priorities
- **Drag-and-Drop**: Reorder jobs to change priorities
- **Job Details**: Click any job for detailed information
- **Real-time Updates**: Live updates every 5 seconds

### Cluster Monitoring
- **Node Status**: Idle, allocated, mixed, down nodes
- **Resource Usage**: CPU and memory statistics
- **Partition Info**: Available partitions and limits
- **Running Jobs**: Currently executing jobs with runtime

## 🔒 Security Notes

### SSH Authentication
- **Password-based**: Uses standard SSH password authentication
- **Secure Storage**: Credentials stored locally in encrypted format
- **No Key Management**: No need to manage SSH keys

### Network Security
- **Local Storage**: All data stored locally on dashboard host
- **Direct SSH**: Direct SSH connections to SLURM hosts
- **No Cloud**: No external services or cloud dependencies

## 🛠️ Troubleshooting

### Can't Connect to Cluster
1. **Test SSH manually**: `ssh username@cluster-host`
2. **Check password**: Verify credentials are correct
3. **Network connectivity**: Ensure cluster is reachable
4. **SSH configuration**: Verify password auth is enabled on cluster

### Dashboard Won't Start
1. **Check Python**: `python --version` (should be 3.7+)
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Check port**: Ensure port 5000 is available
4. **Try Docker**: Use Docker deployment as alternative

### Jobs Not Updating
1. **Check cluster status**: Look for error indicators
2. **Verify SLURM access**: Test SLURM commands manually
3. **Check permissions**: Ensure user has SLURM access
4. **Review logs**: Check console for error messages

## 📊 Example Workflow

### Daily Operations
1. **Open dashboard** (`http://localhost:5000`)
2. **Select cluster** from dropdown
3. **Review queue** - see pending jobs and priorities
4. **Adjust priorities** - drag important jobs to top
5. **Monitor progress** - watch jobs move from queue to running
6. **Switch clusters** - check other environments as needed

### Adding New Environment
1. **Click "Add Cluster"**
2. **Enter details** for new environment
3. **Test connection** to verify access
4. **Save and switch** to new cluster
5. **Begin monitoring** immediately

## 🎉 You're Ready!

The dashboard is now running and ready to manage your SLURM clusters. The interface is intuitive and self-explanatory:

- **Green indicators** = Everything working
- **Red indicators** = Connection issues
- **Drag and drop** = Change job priorities
- **Click for details** = Get more information

**No configuration files to edit, no environment variables to set** - just add your clusters through the web interface and start managing your HPC workloads!

---

**Need help?** Check the comprehensive guides:
- `MULTI_CLUSTER_GUIDE.md` - Detailed multi-cluster features
- `WINDOWS_DEPLOYMENT.md` - Windows-specific instructions  
- `DOCKER_DEPLOYMENT.md` - Docker deployment guide
