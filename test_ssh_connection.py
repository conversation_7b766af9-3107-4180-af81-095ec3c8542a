#!/usr/bin/env python3
"""
Test SSH connection to SLURM host
"""
import paramiko
import socket
import sys

def test_connection(host, username, password, port=22):
    """Test SSH connection and SLURM commands"""
    
    print(f"Testing connection to {host}...")
    print("=" * 50)
    
    # Step 1: Test network connectivity
    print("1. Testing network connectivity...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ Network: Can reach {host}:{port}")
        else:
            print(f"   ❌ Network: Cannot reach {host}:{port}")
            return False
    except Exception as e:
        print(f"   ❌ Network error: {e}")
        return False
    
    # Step 2: Test SSH authentication
    print("2. Testing SSH authentication...")
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh.connect(
            hostname=host,
            username=username,
            password=password,
            timeout=10,
            allow_agent=False,
            look_for_keys=False
        )
        
        print(f"   ✅ SSH: Successfully authenticated as {username}")
        
        # Step 3: Test basic command
        print("3. Testing basic command execution...")
        stdin, stdout, stderr = ssh.exec_command('echo "Hello from SSH"', timeout=10)
        output = stdout.read().decode('utf-8').strip()
        
        if output == "Hello from SSH":
            print("   ✅ Command: Basic command execution works")
        else:
            print("   ❌ Command: Basic command failed")
            ssh.close()
            return False
        
        # Step 4: Test SLURM commands
        print("4. Testing SLURM commands...")
        
        # Test squeue command
        stdin, stdout, stderr = ssh.exec_command('squeue --version', timeout=15)
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8').strip()
        error = stderr.read().decode('utf-8').strip()
        
        if exit_status == 0 and output:
            print(f"   ✅ SLURM: {output}")
        else:
            print(f"   ❌ SLURM: Command failed")
            if error:
                print(f"      Error: {error}")
            ssh.close()
            return False
        
        # Test sinfo command
        print("5. Testing cluster information...")
        stdin, stdout, stderr = ssh.exec_command('sinfo -h', timeout=15)
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8').strip()
        
        if exit_status == 0:
            print("   ✅ Cluster Info: SLURM cluster is accessible")
            if output:
                lines = output.split('\n')[:3]  # Show first 3 lines
                for line in lines:
                    print(f"      {line}")
        else:
            print("   ⚠️  Cluster Info: sinfo command failed (might still work)")
        
        ssh.close()
        print("\n🎉 SUCCESS: All tests passed! This host should work with the dashboard.")
        return True
        
    except paramiko.AuthenticationException:
        print(f"   ❌ SSH: Authentication failed for user '{username}'")
        print("      Check username and password")
        return False
    except paramiko.SSHException as e:
        print(f"   ❌ SSH: Connection error - {e}")
        return False
    except socket.timeout:
        print("   ❌ SSH: Connection timed out")
        return False
    except Exception as e:
        print(f"   ❌ SSH: Unexpected error - {e}")
        return False

if __name__ == "__main__":
    # Test the provided credentials
    host = "************"
    username = "nvme"
    password = "logan"
    
    print("SLURM Host Connection Test")
    print("=" * 50)
    print(f"Host: {host}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    success = test_connection(host, username, password)
    
    if success:
        print("\n✅ RESULT: Host is ready for dashboard!")
        print("You can add this cluster to your dashboard with confidence.")
    else:
        print("\n❌ RESULT: Host has issues that need to be resolved.")
        print("Please check the errors above and fix them before adding to dashboard.")
    
    sys.exit(0 if success else 1)
