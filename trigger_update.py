#!/usr/bin/env python3
"""
Manually trigger dashboard data update
"""
import requests

def trigger_update():
    """Trigger a manual update via the API"""
    
    print("Triggering manual dashboard update...")
    
    try:
        # Try to trigger update via the manual refresh endpoint
        response = requests.post("http://localhost:5000/api/manual_refresh", timeout=10)
        print(f"Manual refresh response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Result: {result}")
        
    except Exception as e:
        print(f"Error triggering update: {e}")
    
    # Wait a moment then check the data
    import time
    time.sleep(2)
    
    print("\nChecking cluster data after update...")
    try:
        response = requests.get("http://localhost:5000/api/clusters", timeout=10)
        if response.status_code == 200:
            clusters = response.json()
            for cluster in clusters:
                print(f"Cluster {cluster['id']}: {cluster['name']} - Status: {cluster['status']}")
                print(f"  Queue: {cluster.get('queue_count', 0)}, Running: {cluster.get('running_count', 0)}")
    except Exception as e:
        print(f"Error checking data: {e}")

if __name__ == "__main__":
    trigger_update()
