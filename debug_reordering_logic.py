#!/usr/bin/env python3
"""
Debug the reordering logic to understand what's happening
"""

def show_reordering_logic():
    """Show the current reordering logic step by step"""
    
    print("SLURM Queue Reordering Logic Analysis")
    print("=" * 50)
    
    # Example: Current queue order
    print("📋 EXAMPLE SCENARIO:")
    print("Current SLURM queue order: [A, B, C, D, E]")
    print("User drags job E (position 5) to position 2")
    print("Desired new order: [A, E, B, C, D]")
    print()
    
    # Show the current logic
    original_order = ['A', 'B', 'C', 'D', 'E']
    desired_order = ['A', 'E', 'B', 'C', 'D']
    
    print("🎯 CURRENT DASHBOARD LOGIC:")
    print(f"1. Original order: {original_order}")
    print(f"2. After drag, visual order: {desired_order}")
    print(f"3. Reverse order for scontrol top: {desired_order[::-1]}")
    print()
    
    # Show step-by-step scontrol commands
    reverse_order = desired_order[::-1]
    print("🔄 SCONTROL TOP COMMANDS (current logic):")
    current_queue = original_order.copy()
    
    for i, job in enumerate(reverse_order):
        step = i + 1
        print(f"Step {step}: scontrol top {job}")
        
        # Simulate what happens in SLURM
        if job in current_queue:
            current_queue.remove(job)
            current_queue.insert(0, job)
        
        print(f"   Queue after step {step}: {current_queue}")
    
    print(f"\n✅ Final queue: {current_queue}")
    print(f"🎯 Desired queue: {desired_order}")
    print(f"❓ Match: {'✅ YES' if current_queue == desired_order else '❌ NO'}")
    
    # Show the problem
    print(f"\n🐛 POTENTIAL ISSUES:")
    print(f"1. The logic assumes all jobs are being reordered")
    print(f"2. But we only want to move ONE job to a new position")
    print(f"3. The reverse order might not work for partial reordering")
    
    # Show better logic
    print(f"\n💡 BETTER LOGIC FOR SINGLE JOB MOVE:")
    print(f"Original: {original_order}")
    print(f"Move job E from position 5 to position 2")
    print(f"")
    print(f"Method 1 - Move job to target position:")
    print(f"1. scontrol top E  → [E, A, B, C, D]")
    print(f"2. scontrol top A  → [A, E, B, C, D] ✅")
    print(f"")
    print(f"Method 2 - Calculate exact sequence:")
    print(f"To get [A, E, B, C, D] from [A, B, C, D, E]:")
    print(f"1. scontrol top D  → [D, A, B, C, E]")
    print(f"2. scontrol top C  → [C, D, A, B, E]") 
    print(f"3. scontrol top B  → [B, C, D, A, E]")
    print(f"4. scontrol top E  → [E, B, C, D, A]")
    print(f"5. scontrol top A  → [A, E, B, C, D] ✅")

def test_current_queue():
    """Test with actual current queue"""
    print(f"\n" + "=" * 50)
    print("TESTING WITH YOUR ACTUAL QUEUE")
    print("=" * 50)
    
    # Your actual queue (from the logs)
    actual_queue = [
        "899983_[206-215]",
        "900202_[6-161]", 
        "900199_[0-161]",
        "900185_[0-215]",
        "900184_[0-215]",
        "900186_[9-161]"
    ]
    
    # Simplify for display
    simple_queue = [job.split('_')[0] for job in actual_queue]
    
    print(f"📋 Your current queue: {simple_queue}")
    print(f"   Positions: {list(range(1, len(simple_queue) + 1))}")
    
    # Test scenario: Move job 6 (900186) to position 2
    print(f"\n🎯 TEST: Move job 900186 from position 6 to position 2")
    
    # What user wants
    new_order = simple_queue.copy()
    moved_job = new_order.pop(5)  # Remove from position 6 (index 5)
    new_order.insert(1, moved_job)  # Insert at position 2 (index 1)
    
    print(f"Desired order: {new_order}")
    
    # Current dashboard logic
    reverse_order = new_order[::-1]
    print(f"Current logic - reverse order: {reverse_order}")
    
    # Simulate the commands
    current_queue = simple_queue.copy()
    print(f"\n🔄 Simulating current logic:")
    
    for i, job in enumerate(reverse_order):
        step = i + 1
        print(f"Step {step}: scontrol top {job}")
        
        if job in current_queue:
            current_queue.remove(job)
            current_queue.insert(0, job)
        
        print(f"   Result: {current_queue}")
    
    print(f"\n📊 RESULTS:")
    print(f"Desired: {new_order}")
    print(f"Actual:  {current_queue}")
    print(f"Match: {'✅ YES' if current_queue == new_order else '❌ NO'}")
    
    if current_queue != new_order:
        print(f"\n🔧 This explains why your reordering isn't working!")

if __name__ == "__main__":
    show_reordering_logic()
    test_current_queue()
