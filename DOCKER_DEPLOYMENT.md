# SLURM Dashboard - Docker Deployment Guide

This guide explains how to deploy the SLURM Dashboard using Docker, allowing you to run the dashboard on any machine while connecting to your SLURM cluster via SSH.

## 🐳 Docker Architecture

```
┌─────────────────┐    SSH    ┌─────────────────┐
│  Docker         │ ────────► │  SLURM Head     │
│  Container      │           │  Node           │
│                 │           │                 │
│ ┌─────────────┐ │           │ ┌─────────────┐ │
│ │ Dashboard   │ │           │ │ squeue      │ │
│ │ Web App     │ │           │ │ sinfo       │ │
│ │             │ │           │ │ scontrol    │ │
│ └─────────────┘ │           │ └─────────────┘ │
└─────────────────┘           └─────────────────┘
        │
        │ HTTP/WebSocket
        ▼
┌─────────────────┐
│   Web Browser   │
│   (Users)       │
└─────────────────┘
```

## 🚀 Quick Start

### 1. Setup SSH Access

First, ensure you can SSH to your SLURM head node with password authentication:

```bash
# Test SSH connection with password
ssh slurmadmin@your-slurm-host

# If password authentication is disabled, enable it on SLURM host:
# Edit /etc/ssh/sshd_config and set:
# PasswordAuthentication yes
# Then restart SSH: sudo systemctl restart sshd

# Test SLURM commands
ssh slurmadmin@your-slurm-host 'squeue --version'
```

### 2. Run Setup Script

```bash
# Make setup script executable
chmod +x docker-setup.sh

# Run setup
./docker-setup.sh
```

### 3. Configure Environment

Edit the `.env` file created by the setup script:

```bash
# SLURM Connection Settings
SLURM_HOST=your-slurm-head-node.example.com
SLURM_USER=slurmadmin
CLUSTER_NAME=Production HPC Cluster

# Dashboard Settings
REFRESH_INTERVAL=5
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key
```

### 4. Deploy with Docker Compose

```bash
# Build and start the dashboard
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop the dashboard
docker-compose down
```

### 5. Access Dashboard

Open your web browser and navigate to:
- Local: `http://localhost:5000`
- Remote: `http://your-docker-host:5000`

## 📁 Directory Structure

```
slurm-dashboard/
├── Dockerfile              # Docker image definition
├── docker-compose.yml      # Docker Compose configuration
├── .env                    # Environment variables
├── .env.example           # Environment template
├── docker-setup.sh        # Setup script
├── ssh/                   # SSH keys directory
│   ├── id_rsa            # Private key for SLURM access
│   └── id_rsa.pub        # Public key
├── logs/                  # Application logs
└── [application files]
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SLURM_HOST` | SLURM head node hostname/IP | `localhost` |
| `SLURM_USER` | SLURM admin user | `slurmadmin` |
| `SLURM_SSH_KEY_PATH` | Path to SSH private key | `/app/ssh/id_rsa` |
| `CLUSTER_NAME` | Display name for cluster | `HPC Cluster` |
| `REFRESH_INTERVAL` | Update interval (seconds) | `5` |
| `MAX_QUEUE_JOBS` | Max jobs to display in queue | `100` |
| `MAX_RUNNING_JOBS` | Max running jobs to display | `50` |
| `FLASK_ENV` | Flask environment | `production` |
| `SECRET_KEY` | Flask secret key | auto-generated |
| `HOST` | Bind address | `0.0.0.0` |
| `PORT` | Port number | `5000` |

### Docker Compose Override

Create `docker-compose.override.yml` for custom settings:

```yaml
version: '3.8'

services:
  slurm-dashboard:
    ports:
      - "8080:5000"  # Custom port
    environment:
      - REFRESH_INTERVAL=10
    volumes:
      - /custom/path/ssh:/app/ssh:ro
```

## 🔒 Security Considerations

### SSH Key Security

1. **Use dedicated SSH key**: Create a specific key for the dashboard
2. **Restrict permissions**: Ensure SSH key has minimal required permissions
3. **Key rotation**: Regularly rotate SSH keys
4. **No password**: Use key-based authentication only

### SLURM User Permissions

Configure sudo permissions for the SLURM user:

```bash
# /etc/sudoers.d/slurm-dashboard
slurmadmin ALL=(ALL) NOPASSWD: /usr/bin/scontrol update JobId=* Priority=*
slurmadmin ALL=(ALL) NOPASSWD: /usr/bin/squeue *
slurmadmin ALL=(ALL) NOPASSWD: /usr/bin/sinfo *
```

### Network Security

1. **Firewall**: Restrict access to dashboard port
2. **HTTPS**: Use reverse proxy with SSL in production
3. **VPN**: Consider VPN access for remote users

## 🔄 Production Deployment

### Using Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name slurm-dashboard.example.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Systemd Service

Create `/etc/systemd/system/slurm-dashboard.service`:

```ini
[Unit]
Description=SLURM Dashboard
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/slurm-dashboard
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable slurm-dashboard
sudo systemctl start slurm-dashboard
```

## 🐛 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   ```bash
   # Test SSH connection manually
   docker exec -it slurm-dashboard ssh -i /app/ssh/id_rsa slurmadmin@your-slurm-host

   # Check SSH key permissions
   ls -la ssh/
   ```

2. **SLURM Commands Not Found**
   ```bash
   # Test SLURM commands on head node
   ssh -i ssh/id_rsa slurmadmin@your-slurm-host 'which squeue'
   ```

3. **Permission Denied**
   ```bash
   # Check sudo configuration on SLURM head node
   ssh -i ssh/id_rsa slurmadmin@your-slurm-host 'sudo -l'
   ```

4. **Container Won't Start**
   ```bash
   # Check logs
   docker-compose logs slurm-dashboard

   # Check container status
   docker-compose ps
   ```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set environment variable
echo "FLASK_ENV=development" >> .env

# Restart container
docker-compose restart
```

### Health Checks

The container includes health checks:

```bash
# Check container health
docker-compose ps

# Manual health check
curl -f http://localhost:5000/api/cluster_info
```

## 📊 Monitoring

### Log Management

```bash
# View real-time logs
docker-compose logs -f

# View specific service logs
docker-compose logs slurm-dashboard

# Log rotation (add to docker-compose.yml)
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### Metrics Collection

Consider integrating with monitoring systems:
- Prometheus metrics endpoint
- Grafana dashboards
- Log aggregation (ELK stack)

## 🔄 Updates and Maintenance

### Updating the Dashboard

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose up --build -d

# Clean up old images
docker image prune
```

### Backup and Recovery

```bash
# Backup configuration
tar -czf slurm-dashboard-backup.tar.gz .env ssh/ logs/

# Restore configuration
tar -xzf slurm-dashboard-backup.tar.gz
```

## 📞 Support

For issues and questions:
1. Check container logs: `docker-compose logs`
2. Verify SSH connectivity
3. Test SLURM commands manually
4. Review environment configuration
