@echo off
echo ==========================================
echo        SLURM Dashboard Startup
echo ==========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing Python dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo Warning: requirements.txt not found
)

REM Check if SLURM commands are available
echo Checking SLURM installation...
squeue --version >nul 2>&1
if errorlevel 1 (
    echo Warning: squeue command not found. Make sure SLURM is installed and in PATH
)

sinfo --version >nul 2>&1
if errorlevel 1 (
    echo Warning: sinfo command not found. Make sure SLURM is installed and in PATH
)

scontrol --version >nul 2>&1
if errorlevel 1 (
    echo Warning: scontrol command not found. Make sure SLURM is installed and in PATH
)

echo Starting SLURM Dashboard...
echo Dashboard will be available at: http://localhost:5000
echo Press Ctrl+C to stop the server
echo ==========================================

REM Start the application
python main.py

pause
