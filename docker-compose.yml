version: '3.8'

services:
  slurm-dashboard:
    build: .
    container_name: slurm-dashboard
    ports:
      - "5000:5000"
    environment:
      - SLURM_HOST=${SLURM_HOST:-localhost}
      - SLURM_USER=${SLURM_USER:-slurmadmin}
      - SLURM_SSH_PASSWORD=${SLURM_SSH_PASSWORD}
      - CLUSTER_NAME=${CLUSTER_NAME:-HPC Cluster}
      - FLASK_ENV=production
    volumes:
      # Mount logs directory
      - ./logs:/app/logs
      # Optional: Mount custom config
      - ./config.py:/app/config.py:ro
    restart: unless-stopped
    networks:
      - slurm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/cluster_info"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  slurm-network:
    driver: bridge
