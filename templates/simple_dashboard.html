<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple SLURM Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .cluster-selector {
            margin: 20px 0;
        }
        .cluster-selector select {
            padding: 10px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .jobs-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .jobs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .jobs-table tr:hover {
            background-color: #f8f9fa;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }
        .status-online { color: #27ae60; }
        .status-offline { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🖥️ Simple SLURM Dashboard</h1>
        <p>Real-time cluster monitoring without complexity</p>
    </div>

    <div class="cluster-selector">
        <label for="clusterSelect">Select Cluster:</label>
        <select id="clusterSelect" onchange="loadClusterData()">
            <option value="">Loading clusters...</option>
        </select>
        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
    </div>

    <div id="clusterInfo" style="display: none;">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Cluster Status</h3>
                <div id="clusterStatus" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Running Jobs</h3>
                <div id="runningCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Queue Jobs</h3>
                <div id="queueCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Total Nodes</h3>
                <div id="nodeCount" class="stat-number">-</div>
            </div>
        </div>

        <div class="jobs-section">
            <h3>🏃 Running Jobs</h3>
            <div id="runningJobsContent">
                <div class="loading">Loading running jobs...</div>
            </div>
        </div>

        <div class="jobs-section">
            <h3>⏳ Queue Jobs</h3>
            <div id="queueJobsContent">
                <div class="loading">Loading queue jobs...</div>
            </div>
        </div>
    </div>

    <script>
        let currentCluster = null;

        // Load clusters on page load
        window.onload = function() {
            loadClusters();
        };

        async function loadClusters() {
            try {
                const response = await fetch('/api/clusters');
                const clusters = await response.json();
                
                const select = document.getElementById('clusterSelect');
                select.innerHTML = '';
                
                if (clusters.length === 0) {
                    select.innerHTML = '<option value="">No clusters configured</option>';
                    return;
                }
                
                select.innerHTML = '<option value="">Select a cluster...</option>';
                clusters.forEach(cluster => {
                    const option = document.createElement('option');
                    option.value = cluster.id;
                    option.textContent = `${cluster.name} (${cluster.host})`;
                    select.appendChild(option);
                });
                
                // Auto-select first cluster
                if (clusters.length > 0) {
                    select.value = clusters[0].id;
                    loadClusterData();
                }
            } catch (error) {
                console.error('Error loading clusters:', error);
                document.getElementById('clusterSelect').innerHTML = '<option value="">Error loading clusters</option>';
            }
        }

        async function loadClusterData() {
            const clusterId = document.getElementById('clusterSelect').value;
            if (!clusterId) {
                document.getElementById('clusterInfo').style.display = 'none';
                return;
            }

            currentCluster = clusterId;
            document.getElementById('clusterInfo').style.display = 'block';
            
            // Show loading state
            document.getElementById('clusterStatus').textContent = 'Loading...';
            document.getElementById('runningCount').textContent = '-';
            document.getElementById('queueCount').textContent = '-';
            document.getElementById('nodeCount').textContent = '-';
            document.getElementById('runningJobsContent').innerHTML = '<div class="loading">Loading running jobs...</div>';
            document.getElementById('queueJobsContent').innerHTML = '<div class="loading">Loading queue jobs...</div>';

            try {
                // Fetch cluster data
                const response = await fetch(`/api/cluster/${clusterId}/data`);
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                    return;
                }

                // Update stats
                document.getElementById('clusterStatus').textContent = data.status || 'Unknown';
                document.getElementById('clusterStatus').className = `stat-number status-${data.status}`;
                document.getElementById('runningCount').textContent = data.running_jobs ? data.running_jobs.length : 0;
                document.getElementById('queueCount').textContent = data.queue_jobs ? data.queue_jobs.length : 0;
                document.getElementById('nodeCount').textContent = data.cluster_info?.nodes?.total || 0;

                // Update running jobs table
                updateJobsTable('runningJobsContent', data.running_jobs || [], 'running');
                
                // Update queue jobs table
                updateJobsTable('queueJobsContent', data.queue_jobs || [], 'queue');

            } catch (error) {
                console.error('Error loading cluster data:', error);
                showError('Failed to load cluster data: ' + error.message);
            }
        }

        function updateJobsTable(containerId, jobs, type) {
            const container = document.getElementById(containerId);
            
            if (jobs.length === 0) {
                container.innerHTML = '<div class="loading">No jobs found</div>';
                return;
            }

            let tableHTML = '<table class="jobs-table"><thead><tr>';
            
            if (type === 'running') {
                tableHTML += '<th>Job ID</th><th>Name</th><th>User</th><th>Time</th><th>Nodes</th>';
            } else {
                tableHTML += '<th>Job ID</th><th>Name</th><th>User</th><th>Priority</th><th>Reason</th>';
            }
            
            tableHTML += '</tr></thead><tbody>';
            
            // Show first 20 jobs
            jobs.slice(0, 20).forEach(job => {
                tableHTML += '<tr>';
                tableHTML += `<td>${job.job_id}</td>`;
                tableHTML += `<td>${job.name}</td>`;
                tableHTML += `<td>${job.user}</td>`;
                
                if (type === 'running') {
                    tableHTML += `<td>${job.time || '-'}</td>`;
                    tableHTML += `<td>${job.nodes || '-'}</td>`;
                } else {
                    tableHTML += `<td>${job.priority || '-'}</td>`;
                    tableHTML += `<td>${job.reason || '-'}</td>`;
                }
                
                tableHTML += '</tr>';
            });
            
            tableHTML += '</tbody></table>';
            
            if (jobs.length > 20) {
                tableHTML += `<p><em>Showing first 20 of ${jobs.length} jobs</em></p>`;
            }
            
            container.innerHTML = tableHTML;
        }

        function showError(message) {
            const errorHTML = `<div class="error">Error: ${message}</div>`;
            document.getElementById('runningJobsContent').innerHTML = errorHTML;
            document.getElementById('queueJobsContent').innerHTML = errorHTML;
        }

        function refreshData() {
            if (currentCluster) {
                loadClusterData();
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (currentCluster) {
                loadClusterData();
            }
        }, 30000);
    </script>
</body>
</html>
