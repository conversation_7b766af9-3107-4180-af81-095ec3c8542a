<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 SLURM Dashboard Test Page</h1>
        
        <div class="status info">
            <strong>Testing Dashboard Components...</strong>
        </div>

        <div id="test-status">
            <div class="status success">✅ HTML Page Loaded Successfully</div>
            <div class="status success">✅ CSS Styles Applied</div>
            <div class="status success" id="js-status">⏳ Testing JavaScript...</div>
        </div>

        <h3>API Tests:</h3>
        <button onclick="testClusters()">Test /api/clusters</button>
        <button onclick="testClusterData()">Test Cluster Data</button>
        <button onclick="testConnection()">Test Connection</button>

        <div id="results"></div>

        <h3>Dashboard Link:</h3>
        <a href="/" style="color: #007bff; text-decoration: none; font-weight: bold;">
            🔗 Go to Main Dashboard
        </a>
    </div>

    <script>
        // Test JavaScript execution
        document.getElementById('js-status').innerHTML = '✅ JavaScript Working';
        document.getElementById('js-status').className = 'status success';

        async function testClusters() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing /api/clusters...\n';
            
            try {
                const response = await fetch('/api/clusters');
                const data = await response.json();
                
                results.innerHTML += `Status: ${response.status}\n`;
                results.innerHTML += `Clusters found: ${data.length}\n`;
                results.innerHTML += `Data: ${JSON.stringify(data, null, 2)}\n`;
                
                if (data.length > 0) {
                    results.innerHTML += `\n✅ SUCCESS: Found cluster "${data[0].name}" at ${data[0].host}\n`;
                } else {
                    results.innerHTML += `\n⚠️ No clusters configured\n`;
                }
            } catch (error) {
                results.innerHTML += `\n❌ ERROR: ${error.message}\n`;
            }
        }

        async function testClusterData() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing cluster data...\n';
            
            try {
                // First get clusters
                const clustersResponse = await fetch('/api/clusters');
                const clusters = await clustersResponse.json();
                
                if (clusters.length === 0) {
                    results.innerHTML += '❌ No clusters to test\n';
                    return;
                }
                
                const clusterId = clusters[0].id;
                results.innerHTML += `Testing cluster: ${clusterId}\n`;
                
                // Test cluster data
                const dataResponse = await fetch(`/api/cluster/${clusterId}/data`);
                const data = await dataResponse.json();
                
                results.innerHTML += `Status: ${dataResponse.status}\n`;
                results.innerHTML += `Queue jobs: ${data.queue_jobs ? data.queue_jobs.length : 0}\n`;
                results.innerHTML += `Running jobs: ${data.running_jobs ? data.running_jobs.length : 0}\n`;
                results.innerHTML += `Cluster status: ${data.status || 'unknown'}\n`;
                
                if (data.running_jobs && data.running_jobs.length > 0) {
                    results.innerHTML += `\n✅ SUCCESS: Found ${data.running_jobs.length} running jobs!\n`;
                    results.innerHTML += `Sample job: ${data.running_jobs[0].job_id} - ${data.running_jobs[0].name}\n`;
                } else {
                    results.innerHTML += `\n⚠️ No running jobs found\n`;
                }
                
            } catch (error) {
                results.innerHTML += `\n❌ ERROR: ${error.message}\n`;
            }
        }

        async function testConnection() {
            const results = document.getElementById('results');
            results.innerHTML = 'Testing connection to ************...\n';
            
            try {
                const response = await fetch('/api/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        host: '************',
                        username: 'nvme',
                        password: 'logan',
                        port: 22
                    })
                });
                
                const result = await response.json();
                
                results.innerHTML += `Status: ${response.status}\n`;
                results.innerHTML += `Success: ${result.success}\n`;
                
                if (result.success) {
                    results.innerHTML += `✅ SUCCESS: ${result.message}\n`;
                    if (result.slurm_version) {
                        results.innerHTML += `SLURM Version: ${result.slurm_version}\n`;
                    }
                } else {
                    results.innerHTML += `❌ FAILED: ${result.error}\n`;
                }
                
            } catch (error) {
                results.innerHTML += `\n❌ ERROR: ${error.message}\n`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(() => {
                testClusters();
            }, 1000);
        };
    </script>
</body>
</html>
