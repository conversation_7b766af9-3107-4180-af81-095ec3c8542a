<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ cluster_name }} - SLURM Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DISABLED: SocketIO removed to fix loading issues -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-server"></i> Multi-Cluster SLURM Dashboard</h1>
                <div class="header-controls">
                    <div class="cluster-selector">
                        <select id="cluster-select">
                            <option value="">Select Cluster...</option>
                        </select>
                    </div>
                    <button class="add-cluster-btn" id="add-cluster-btn">
                        <i class="fas fa-plus"></i> Add Cluster
                    </button>
                    <div class="status-indicator" id="connection-status">
                        <i class="fas fa-circle"></i>
                        <span>Connected</span>
                    </div>
                    <div class="last-update">
                        Last Update: <span id="last-update-time">--</span>
                    </div>
                    <button class="refresh-btn" id="manual-refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Sidebar - Queue Management -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h2><i class="fas fa-clock"></i> Job Queue</h2>
                    <div class="queue-stats">
                        <span id="queue-count">0</span> jobs pending
                    </div>
                    <div class="cluster-info" id="current-cluster-info">
                        <small>No cluster selected</small>
                    </div>
                </div>

                <div class="queue-controls">
                    <div class="priority-legend">
                        <div class="legend-item">
                            <div class="priority-color high"></div>
                            <span>High Priority</span>
                        </div>
                        <div class="legend-item">
                            <div class="priority-color medium"></div>
                            <span>Medium Priority</span>
                        </div>
                        <div class="legend-item">
                            <div class="priority-color low"></div>
                            <span>Low Priority</span>
                        </div>
                    </div>
                </div>

                <div class="queue-container" id="queue-container">
                    <div class="queue-list" id="queue-list">
                        <!-- Queue jobs will be populated here -->
                    </div>
                </div>
            </aside>

            <!-- Main Dashboard Area -->
            <main class="dashboard-main">
                <!-- Cluster Overview -->
                <section class="cluster-overview">
                    <h2><i class="fas fa-chart-pie"></i> Cluster Overview</h2>

                    <div class="overview-grid">
                        <div class="overview-card">
                            <div class="card-header">
                                <h3><i class="fas fa-server"></i> Nodes</h3>
                            </div>
                            <div class="card-content">
                                <div class="stat-row">
                                    <span class="stat-label">Total:</span>
                                    <span class="stat-value" id="total-nodes">--</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Idle:</span>
                                    <span class="stat-value idle" id="idle-nodes">--</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Allocated:</span>
                                    <span class="stat-value allocated" id="allocated-nodes">--</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Mixed:</span>
                                    <span class="stat-value mixed" id="mixed-nodes">--</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Down:</span>
                                    <span class="stat-value down" id="down-nodes">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="overview-card">
                            <div class="card-header">
                                <h3><i class="fas fa-microchip"></i> Resources</h3>
                            </div>
                            <div class="card-content">
                                <div class="stat-row">
                                    <span class="stat-label">Total CPUs:</span>
                                    <span class="stat-value" id="total-cpus">--</span>
                                </div>
                                <div class="stat-row">
                                    <span class="stat-label">Total Memory:</span>
                                    <span class="stat-value" id="total-memory">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="overview-card">
                            <div class="card-header">
                                <h3><i class="fas fa-layer-group"></i> Partitions</h3>
                            </div>
                            <div class="card-content" id="partitions-list">
                                <!-- Partitions will be populated here -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Running Jobs Section -->
                <section class="running-jobs">
                    <div class="section-header">
                        <h2><i class="fas fa-play"></i> Running Jobs</h2>
                        <div class="running-stats">
                            <span id="running-count">0</span> jobs running
                        </div>
                    </div>

                    <div class="jobs-table-container">
                        <table class="jobs-table" id="running-jobs-table">
                            <thead>
                                <tr>
                                    <th>Job ID</th>
                                    <th>Name</th>
                                    <th>User</th>
                                    <th>Partition</th>
                                    <th>Runtime</th>
                                    <th>Nodes</th>
                                </tr>
                            </thead>
                            <tbody id="running-jobs-tbody">
                                <!-- Running jobs will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Job Details Modal -->
    <div class="modal" id="job-details-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Job Details</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="job-details-content">
                <!-- Job details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Add/Edit Cluster Modal -->
    <div class="modal" id="cluster-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="cluster-modal-title">Add New Cluster</h3>
                <button class="modal-close" id="cluster-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="cluster-form">
                    <div class="form-group">
                        <label for="cluster-name">Display Name *</label>
                        <input type="text" id="cluster-name" name="name" required placeholder="e.g., Production HPC, Dev Cluster">
                        <small class="form-help">Friendly name for this cluster (dashboard display only)</small>
                    </div>
                    <div class="form-group">
                        <label for="cluster-host">SLURM Host/IP Address *</label>
                        <input type="text" id="cluster-host" name="host" required placeholder="e.g., slurm-head.company.com">
                        <small class="form-help">Hostname or IP of your SLURM head node</small>
                    </div>
                    <div class="form-group">
                        <label for="cluster-username">SSH Username *</label>
                        <input type="text" id="cluster-username" name="username" required placeholder="e.g., slurmadmin">
                        <small class="form-help">SSH user with SLURM access</small>
                    </div>
                    <div class="form-group">
                        <label for="cluster-password">SSH Password *</label>
                        <input type="password" id="cluster-password" name="password" required>
                        <small class="form-help">SSH password for authentication</small>
                    </div>
                    <div class="form-group">
                        <label for="cluster-port">SSH Port</label>
                        <input type="number" id="cluster-port" name="port" value="22" min="1" max="65535">
                    </div>
                    <div class="form-group">
                        <label for="cluster-description">Description</label>
                        <textarea id="cluster-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="cluster-enabled" name="enabled" checked>
                            <span class="checkmark"></span>
                            Enable this cluster
                        </label>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="test-connection-btn" class="btn btn-secondary">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                        <button type="button" id="save-cluster-btn" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Cluster
                        </button>
                        <button type="button" id="cancel-cluster-btn" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cluster Management Panel -->
    <div class="cluster-panel" id="cluster-panel">
        <div class="cluster-panel-header">
            <h3><i class="fas fa-cogs"></i> Cluster Management</h3>
            <button class="panel-close" id="cluster-panel-close">&times;</button>
        </div>
        <div class="cluster-panel-body">
            <div class="cluster-list" id="cluster-list">
                <!-- Cluster list will be populated here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/multi-cluster-dashboard.js') }}"></script>
    <script>
        // Initialize dashboard with configuration
        window.DASHBOARD_CONFIG = {
            refreshInterval: {{ refresh_interval * 1000 }}
        };
    </script>
</body>
</html>
