<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLURM Dashboard - Priority Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .cluster-selector {
            margin: 20px 0;
        }
        .cluster-selector select {
            padding: 10px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
            margin-right: 10px;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .jobs-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .queue-section {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .queue-jobs-list {
            margin-top: 15px;
        }
        .queue-job {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
        }
        .queue-job:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .queue-job.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .queue-job.sortable-ghost {
            background: #e3f2fd;
            border: 2px dashed #2196f3;
        }
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .job-id {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        .job-priority {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .job-priority.high { background: #e74c3c; }
        .job-priority.medium { background: #f39c12; }
        .job-priority.low { background: #95a5a6; }
        .job-name {
            font-size: 1.1em;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .job-details {
            display: flex;
            gap: 15px;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 8px;
        }
        .job-reason {
            font-size: 0.9em;
            color: #e74c3c;
            font-style: italic;
        }
        .priority-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: none;
        }
        .queue-job:hover .priority-actions {
            display: block;
        }
        .priority-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            margin-left: 5px;
        }
        .priority-btn:hover {
            background: #219a52;
        }
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .jobs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .jobs-table tr:hover {
            background-color: #f8f9fa;
        }
        .loading {
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }
        .status-online { color: #27ae60; }
        .status-offline { color: #e74c3c; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            display: none;
        }
        .notification.success { background: #27ae60; }
        .notification.error { background: #e74c3c; }
        .notification.info { background: #3498db; }
        .drag-instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #0c5460;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            opacity: 0.7;
        }

        .form-group {
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .form-group:first-of-type {
            margin-top: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }

        .form-actions {
            padding: 20px;
            text-align: right;
            border-top: 1px solid #eee;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-left: 10px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #219a52;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        /* Cluster List Styles */
        .cluster-list {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .cluster-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }

        .cluster-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .cluster-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .cluster-item-name {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .cluster-item-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .cluster-item-status.online {
            background: #d4edda;
            color: #155724;
        }

        .cluster-item-status.offline {
            background: #f8d7da;
            color: #721c24;
        }

        .cluster-item-info {
            color: #666;
            margin-bottom: 10px;
        }

        .cluster-item-actions {
            display: flex;
            gap: 10px;
        }

        .cluster-item-actions .btn {
            padding: 5px 10px;
            font-size: 0.9em;
            margin-left: 0;
        }

        /* Save Order Section */
        .save-order-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideDown 0.3s ease-out;
        }

        .save-order-info {
            color: #856404;
            font-size: 0.95em;
        }

        .save-order-info strong {
            color: #533f03;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
</head>
<body>
    <div class="header">
        <h1>🖥️ SLURM Dashboard - Priority Management</h1>
        <p>Drag and drop jobs to change priority • Real-time cluster monitoring</p>
    </div>

    <div class="cluster-selector">
        <label for="clusterSelect">Select Cluster:</label>
        <select id="clusterSelect">
            <option value="">Loading clusters...</option>
        </select>
        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
        <button class="refresh-btn" onclick="showClusterManagement()">⚙️ Manage Clusters</button>
        <button class="refresh-btn" onclick="showAddClusterModal()">➕ Add Cluster</button>
    </div>

    <div id="clusterInfo" style="display: none;">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Cluster Status</h3>
                <div id="clusterStatus" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Queue Jobs</h3>
                <div id="queueCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Running Jobs</h3>
                <div id="runningCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Total Nodes</h3>
                <div id="nodeCount" class="stat-number">-</div>
            </div>
        </div>

        <!-- QUEUE JOBS SECTION - TOP PRIORITY -->
        <div class="jobs-section queue-section">
            <h3>⏳ Job Queue - Priority Management</h3>
            <div class="drag-instructions">
                <strong>💡 Priority Management:</strong>
                • <strong>Drag & Drop:</strong> Drag jobs up/down to reorder, then click "Save Order"
                • <strong>Top Priority:</strong> Click "🔝 Top Priority" for instant highest priority
                • <strong>Save Order:</strong> Apply your custom arrangement to SLURM queue
            </div>

            <!-- Save Order Button (hidden by default) -->
            <div id="saveOrderSection" class="save-order-section" style="display: none;">
                <div class="save-order-info">
                    <strong>📋 Queue order has been changed!</strong>
                    <span id="orderChangeInfo"></span>
                </div>
                <button class="btn btn-success" onclick="saveQueueOrder()">💾 Save New Order</button>
                <button class="btn btn-secondary" onclick="cancelOrderChange()">❌ Cancel Changes</button>
            </div>
            <div id="queueJobsContent">
                <div class="loading">Loading queue jobs...</div>
            </div>
        </div>

        <!-- RUNNING JOBS SECTION -->
        <div class="jobs-section">
            <h3>🏃 Running Jobs</h3>
            <div id="runningJobsContent">
                <div class="loading">Loading running jobs...</div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <!-- Add/Edit Cluster Modal -->
    <div id="clusterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="clusterModalTitle">Add New Cluster</h2>
                <span class="close" onclick="closeModal('clusterModal')">&times;</span>
            </div>
            <form id="clusterForm">
                <div class="form-group">
                    <label for="clusterName">Cluster Name *</label>
                    <input type="text" id="clusterName" required placeholder="e.g., Production Cluster">
                </div>
                <div class="form-group">
                    <label for="clusterHost">Host/IP Address *</label>
                    <input type="text" id="clusterHost" required placeholder="e.g., ************">
                </div>
                <div class="form-group">
                    <label for="clusterUsername">Username *</label>
                    <input type="text" id="clusterUsername" required placeholder="e.g., nvme">
                </div>
                <div class="form-group">
                    <label for="clusterPassword">Password *</label>
                    <input type="password" id="clusterPassword" required placeholder="SSH password">
                </div>
                <div class="form-group">
                    <label for="clusterPort">SSH Port</label>
                    <input type="number" id="clusterPort" value="22" placeholder="22">
                </div>
                <div class="form-group">
                    <label for="clusterDescription">Description</label>
                    <textarea id="clusterDescription" rows="3" placeholder="Optional description"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('clusterModal')">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="testConnection()">🔍 Test Connection</button>
                    <button type="button" class="btn btn-success" onclick="saveCluster()">💾 Save Cluster</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Cluster Management Modal -->
    <div id="clusterManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Cluster Management</h2>
                <span class="close" onclick="closeModal('clusterManagementModal')">&times;</span>
            </div>
            <div id="clusterList" class="cluster-list">
                <div class="loading">Loading clusters...</div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-success" onclick="showAddClusterModal()">➕ Add New Cluster</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('clusterManagementModal')">Close</button>
            </div>
        </div>
    </div>

    <script>
        console.log('Enhanced Dashboard JavaScript starting...');

        let currentCluster = null;
        let sortable = null;
        let originalQueueOrder = [];
        let pendingQueueOrder = [];
        let hasUnsavedChanges = false;

        // Show that JavaScript is working
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing enhanced dashboard...');
            showNotification('Dashboard loading...', 'info');
            loadClusters();
        });

        async function loadClusters() {
            console.log('Loading clusters...');
            try {
                const response = await fetch('/api/clusters');
                console.log('Clusters response:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const clusters = await response.json();
                console.log('Clusters data:', clusters);

                const select = document.getElementById('clusterSelect');
                select.innerHTML = '';

                if (clusters.length === 0) {
                    select.innerHTML = '<option value="">No clusters configured</option>';
                    showNotification('No clusters found', 'error');
                    return;
                }

                select.innerHTML = '<option value="">Select a cluster...</option>';
                clusters.forEach(cluster => {
                    const option = document.createElement('option');
                    option.value = cluster.id;
                    option.textContent = `${cluster.name} (${cluster.host})`;
                    select.appendChild(option);
                });

                // Auto-select first cluster
                if (clusters.length > 0) {
                    select.value = clusters[0].id;
                    loadClusterData(clusters[0].id);
                }

                showNotification(`Found ${clusters.length} cluster(s)`, 'success');

            } catch (error) {
                console.error('Error loading clusters:', error);
                document.getElementById('clusterSelect').innerHTML = '<option value="">Error loading clusters</option>';
                showNotification('Failed to load clusters: ' + error.message, 'error');
            }
        }

        async function loadClusterData(clusterId) {
            if (!clusterId) {
                document.getElementById('clusterInfo').style.display = 'none';
                return;
            }

            console.log('Loading data for cluster:', clusterId);
            currentCluster = clusterId;
            document.getElementById('clusterInfo').style.display = 'block';

            // Show loading state
            document.getElementById('clusterStatus').textContent = 'Loading...';
            document.getElementById('runningCount').textContent = '-';
            document.getElementById('queueCount').textContent = '-';
            document.getElementById('nodeCount').textContent = '-';

            try {
                const response = await fetch(`/api/cluster/${clusterId}/data`);
                console.log('Cluster data response:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                console.log('Cluster data:', data);

                if (data.error) {
                    throw new Error(data.error);
                }

                // Update stats
                document.getElementById('clusterStatus').textContent = data.status || 'Unknown';
                document.getElementById('clusterStatus').className = `stat-number status-${data.status}`;
                document.getElementById('runningCount').textContent = data.running_jobs ? data.running_jobs.length : 0;
                document.getElementById('queueCount').textContent = data.queue_jobs ? data.queue_jobs.length : 0;
                document.getElementById('nodeCount').textContent = data.cluster_info?.nodes?.total || 0;

                // Update queue jobs (with drag-and-drop)
                updateQueueJobs(data.queue_jobs || []);

                // Update running jobs table
                updateRunningJobsTable(data.running_jobs || []);

                showNotification('Data loaded successfully', 'success');

            } catch (error) {
                console.error('Error loading cluster data:', error);
                showNotification('Failed to load cluster data: ' + error.message, 'error');
            }
        }

        function updateQueueJobs(jobs) {
            const container = document.getElementById('queueJobsContent');

            if (jobs.length === 0) {
                container.innerHTML = '<div class="loading">No jobs in queue</div>';
                // Clear saved orders
                originalQueueOrder = [];
                pendingQueueOrder = [];
                hideOrderSaveSection();
                return;
            }

            // Sort jobs by priority (highest first)
            jobs.sort((a, b) => (parseInt(b.priority) || 0) - (parseInt(a.priority) || 0));

            // Save the original order (this is the current SLURM queue order)
            originalQueueOrder = jobs.map(job => job.job_id);
            pendingQueueOrder = [...originalQueueOrder]; // Copy of original
            hasUnsavedChanges = false;

            console.log('📋 Original queue order saved:', originalQueueOrder);

            let html = '<div class="queue-jobs-list" id="queue-jobs-list">';

            jobs.forEach((job, index) => {
                const priorityClass = getPriorityClass(job.priority);
                html += `
                    <div class="queue-job" data-job-id="${job.job_id}" data-priority="${job.priority}">
                        <div class="job-header">
                            <span class="job-id">#${job.job_id}</span>
                            <span class="job-priority ${priorityClass}">${job.priority}</span>
                        </div>
                        <div class="job-name">${escapeHtml(job.name)}</div>
                        <div class="job-details">
                            <span><strong>User:</strong> ${escapeHtml(job.user)}</span>
                            <span><strong>Partition:</strong> ${escapeHtml(job.partition)}</span>
                        </div>
                        <div class="job-reason">${escapeHtml(job.reason)}</div>
                        <div class="priority-actions">
                            <button class="priority-btn" onclick="setTopPriority('${job.job_id}')">🔝 Top Priority</button>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;

            // Hide save order section initially
            hideOrderSaveSection();

            // Initialize drag-and-drop
            initializeSortable();
        }

        function initializeSortable() {
            const queueList = document.getElementById('queue-jobs-list');
            if (!queueList) return;

            if (sortable) {
                sortable.destroy();
            }

            sortable = Sortable.create(queueList, {
                animation: 200,
                ghostClass: 'sortable-ghost',
                chosenClass: 'queue-job-chosen',
                dragClass: 'queue-job-dragging',
                forceFallback: false,
                fallbackOnBody: true,
                swapThreshold: 0.65,
                direction: 'vertical',
                onStart: function(evt) {
                    console.log('🖱️ Drag started:', evt.item.dataset.jobId, 'from position', evt.oldIndex);
                    // Stop auto-refresh during drag
                    if (window.autoRefreshTimer) {
                        clearInterval(window.autoRefreshTimer);
                        console.log('⏸️ Auto-refresh paused during drag');
                    }
                },
                onEnd: function(evt) {
                    console.log('🖱️ Drag ended:', {
                        jobId: evt.item.dataset.jobId,
                        oldIndex: evt.oldIndex,
                        newIndex: evt.newIndex,
                        moved: evt.oldIndex !== evt.newIndex
                    });

                    // CRITICAL FIX: Wait a moment for DOM to settle
                    setTimeout(() => {
                        if (evt.oldIndex !== evt.newIndex) {
                            console.log('📋 Position changed - applying new order');
                            handlePriorityChange(evt);
                        } else {
                            console.log('📋 No position change - restarting auto-refresh');
                            startAutoRefresh();
                        }
                    }, 100); // 100ms delay to let DOM settle
                },
                onMove: function(evt) {
                    console.log('🔄 Moving:', evt.dragged.dataset.jobId, 'to position', evt.newIndex);
                    return true; // Allow the move
                }
            });
        }

        function handlePriorityChange(evt) {
            const jobElement = evt.item;
            const jobId = jobElement.dataset.jobId;
            const newIndex = evt.newIndex;
            const oldIndex = evt.oldIndex;

            if (newIndex === oldIndex) {
                console.log('No position change, restarting auto-refresh');
                startAutoRefresh();
                return;
            }

            console.log(`🖱️ DRAG COMPLETED: Job ${jobId} moved from position ${oldIndex + 1} to ${newIndex + 1}`);

            // CRITICAL FIX: Get the current visual order after DOM has settled
            // Wait a bit more to ensure DOM is completely updated
            setTimeout(() => {
                const allJobElements = document.querySelectorAll('.queue-job');
                const currentVisualOrder = Array.from(allJobElements).map(el => el.dataset.jobId);

                console.log('📋 ORIGINAL ORDER:', originalQueueOrder.map(id => id.split('_')[0]));
                console.log('📋 CURRENT VISUAL ORDER:', currentVisualOrder.map(id => id.split('_')[0]));

                // CRITICAL: Verify the dragged job is in the visual order
                const jobPosition = currentVisualOrder.indexOf(jobId);
                console.log(`🔍 VERIFICATION: Job ${jobId.split('_')[0]} is now at visual position ${jobPosition + 1} (expected: ${newIndex + 1})`);

                if (jobPosition === -1) {
                    console.error('❌ CRITICAL ERROR: Dragged job disappeared from visual order!');
                    console.log('Refreshing to restore correct state...');
                    loadClusterData(currentCluster);
                    return;
                }

                if (jobPosition !== newIndex) {
                    console.warn(`⚠️ Position mismatch: Expected ${newIndex + 1}, actual ${jobPosition + 1}`);
                }

                // Update pending order to match current visual state
                pendingQueueOrder = [...currentVisualOrder];

                // Check if order actually changed from original
                const orderChanged = !arraysEqual(originalQueueOrder, currentVisualOrder);

                if (orderChanged) {
                    hasUnsavedChanges = true;
                    showOrderSaveSection();

                    // Show detailed change info
                    const changeDesc = getDetailedChangeDescription(originalQueueOrder, currentVisualOrder);
                    console.log('📋 CHANGES:', changeDesc);

                    showNotification(`Queue order changed. Click "Save New Order" to apply.`, 'info');
                } else {
                    hasUnsavedChanges = false;
                    hideOrderSaveSection();
                    startAutoRefresh();
                }
            }, 50); // Additional 50ms delay for DOM to fully settle
        }

        async function setTopPriority(jobId) {
            if (!currentCluster) {
                showNotification('No cluster selected', 'error');
                return;
            }

            try {
                // Extract base job ID for display
                const baseJobId = jobId.split('_')[0];
                showNotification(`Setting job ${jobId} (base ID: ${baseJobId}) to top priority...`, 'info');

                const response = await fetch(`/api/cluster/${currentCluster}/set_top_priority`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ job_id: jobId })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ ${result.message}`, 'success');
                    // Refresh data to show new order
                    setTimeout(() => loadClusterData(currentCluster), 1000);
                } else {
                    showNotification(`❌ Failed: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error setting top priority:', error);
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function applyQueueOrder(desiredOrder) {
            if (!currentCluster || !desiredOrder || desiredOrder.length === 0) return;

            try {
                console.log('🎯 APPLYING SMART QUEUE ORDER');
                console.log('Original SLURM order:', originalQueueOrder);
                console.log('Desired new order:', desiredOrder);

                // SMART LOGIC: Only move jobs that actually changed position
                const jobsToMove = calculateMinimalMoves(originalQueueOrder, desiredOrder);

                if (jobsToMove.length === 0) {
                    showNotification('No changes needed', 'info');
                    return;
                }

                console.log('🧠 MULTI-JOB REORDERING: Applying precise order changes');
                console.log('Jobs to reorder:', jobsToMove.map(m => `${m.jobId.split('_')[0]} (step ${m.stepNumber})`));

                showNotification(`Reordering ${jobsToMove.length} jobs to achieve exact order...`, 'info');

                let successCount = 0;
                let failCount = 0;

                // Apply moves in the correct order
                for (let i = 0; i < jobsToMove.length; i++) {
                    const move = jobsToMove[i];
                    const jobId = move.jobId;
                    const baseJobId = jobId.split('_')[0];
                    const stepNum = i + 1;

                    console.log(`🔄 Step ${stepNum}/${jobsToMove.length}: scontrol top ${baseJobId} (${move.reason})`);

                    try {
                        const response = await fetch(`/api/cluster/${currentCluster}/set_top_priority`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ job_id: jobId })
                        });

                        const result = await response.json();

                        if (result.success) {
                            console.log(`✅ Step ${stepNum} SUCCESS: ${result.message}`);
                            successCount++;
                        } else {
                            console.error(`❌ Step ${stepNum} FAILED: ${result.error}`);
                            failCount++;
                            showNotification(`Step ${stepNum} failed: ${result.error}`, 'error');
                        }

                        // Delay between commands
                        if (i < jobsToMove.length - 1) {
                            console.log(`⏱️ Waiting 1000ms before next command...`);
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }

                    } catch (error) {
                        console.error(`❌ Step ${stepNum} ERROR:`, error);
                        failCount++;
                        showNotification(`Step ${stepNum} error: ${error.message}`, 'error');
                    }
                }

                // Show final results
                if (failCount === 0) {
                    showNotification(`✅ Queue reordering completed! (${successCount}/${jobsToMove.length} moves)`, 'success');
                } else {
                    showNotification(`⚠️ Reordering completed with ${failCount} failures (${successCount}/${jobsToMove.length} successful)`, 'error');
                }

                console.log(`📋 SMART REORDERING COMPLETE: ${successCount} success, ${failCount} failed`);

                // Refresh data after a delay
                setTimeout(() => {
                    console.log('🔄 Refreshing data to show new queue order...');
                    loadClusterData(currentCluster);
                    startAutoRefresh();
                }, 2500);

            } catch (error) {
                console.error('❌ Error applying queue order:', error);
                showNotification(`❌ Error reordering queue: ${error.message}`, 'error');
                loadClusterData(currentCluster);
                startAutoRefresh();
            }
        }

        function calculateMinimalMoves(originalOrder, desiredOrder) {
            console.log('🧠 Calculating moves for multi-job reordering...');
            console.log('Original order:', originalOrder.map(id => id.split('_')[0]));
            console.log('Desired order:', desiredOrder.map(id => id.split('_')[0]));

            // CORRECT APPROACH: Use the exact desired order with reverse scontrol top
            // This is the mathematically correct way to achieve any desired order

            console.log('🎯 Using REVERSE ORDER approach for precise reordering');
            console.log('This will execute scontrol top commands in reverse order to achieve exact desired arrangement');

            // Create the reverse order for scontrol top commands
            const reverseOrder = [...desiredOrder].reverse();

            console.log('Commands will be executed in this order:');
            reverseOrder.forEach((jobId, index) => {
                const step = index + 1;
                const baseJobId = jobId.split('_')[0];
                console.log(`  Step ${step}: scontrol top ${baseJobId}`);
            });

            console.log('Expected final order:', desiredOrder.map(id => id.split('_')[0]));

            return reverseOrder.map((jobId, index) => ({
                jobId: jobId,
                targetPosition: desiredOrder.length - index,
                reason: `Step ${index + 1}: Move ${jobId.split('_')[0]} to build final order`,
                stepNumber: index + 1,
                totalSteps: reverseOrder.length
            }));
        }

        async function updateJobPriority(jobId, newPriority) {
            // This function is now deprecated in favor of applyQueueOrder
            // Keeping it for backward compatibility
            console.log('updateJobPriority is deprecated, use applyQueueOrder instead');
        }

        function updateRunningJobsTable(jobs) {
            const container = document.getElementById('runningJobsContent');

            if (jobs.length === 0) {
                container.innerHTML = '<div class="loading">No jobs currently running</div>';
                return;
            }

            let tableHTML = '<table class="jobs-table"><thead><tr>';
            tableHTML += '<th>Job ID</th><th>Name</th><th>User</th><th>Runtime</th><th>Max Time</th><th>Nodes</th><th>Actions</th>';
            tableHTML += '</tr></thead><tbody>';

            // Show all running jobs (grouped by base job ID)
            jobs.forEach(job => {
                const baseJobId = job.job_id;
                const displayJobId = job.display_job_id || job.job_id;
                const jobCount = job.job_count || 1;

                tableHTML += '<tr>';
                tableHTML += `<td><strong>#${displayJobId}</strong></td>`;
                tableHTML += `<td>${escapeHtml(job.name)}</td>`;
                tableHTML += `<td>${escapeHtml(job.user)}</td>`;
                tableHTML += `<td>${escapeHtml(job.time || '-')}</td>`;
                tableHTML += `<td>${escapeHtml(job.time_limit || 'N/A')}</td>`;
                tableHTML += `<td>${escapeHtml(job.nodes || '-')}</td>`;
                tableHTML += `<td><button class="requeue-btn" onclick="showRequeueConfirmation('${baseJobId}', '${escapeHtml(displayJobId)}', '${escapeHtml(job.name)}', '${escapeHtml(job.time || '-')}', ${jobCount})" title="Requeue ${jobCount} job(s)">🔄 Requeue (${jobCount})</button></td>`;
                tableHTML += '</tr>';
            });

            tableHTML += '</tbody></table>';

            container.innerHTML = tableHTML;
        }

        function getPriorityClass(priority) {
            const priorityNum = parseInt(priority) || 0;
            if (priorityNum >= 1000) return 'high';
            if (priorityNum >= 100) return 'medium';
            return 'low';
        }

        function refreshData() {
            if (currentCluster) {
                loadClusterData(currentCluster);
            } else {
                loadClusters();
            }
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';

            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Loading functions
        function showLoading() {
            // Create loading overlay if it doesn't exist
            let overlay = document.getElementById('loadingOverlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.id = 'loadingOverlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    font-size: 18px;
                    color: #333;
                `;
                overlay.innerHTML = '<div style="text-align: center;"><div style="font-size: 24px; margin-bottom: 10px;">🔄</div>Loading...</div>';
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // Handle cluster selection
        document.getElementById('clusterSelect').addEventListener('change', function(e) {
            loadClusterData(e.target.value);
        });

        // Auto-refresh every 30 seconds (with timer management)
        function startAutoRefresh() {
            window.autoRefreshTimer = setInterval(() => {
                if (currentCluster) {
                    loadClusterData(currentCluster);
                }
            }, 30000);
        }

        // Start auto-refresh
        startAutoRefresh();

        // Cluster Management Functions
        function showClusterManagement() {
            document.getElementById('clusterManagementModal').style.display = 'block';
            loadClusterManagement();
        }

        function showAddClusterModal() {
            document.getElementById('clusterModalTitle').textContent = 'Add New Cluster';
            clearClusterForm();
            document.getElementById('clusterModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function clearClusterForm() {
            document.getElementById('clusterName').value = '';
            document.getElementById('clusterHost').value = '';
            document.getElementById('clusterUsername').value = '';
            document.getElementById('clusterPassword').value = '';
            document.getElementById('clusterPort').value = '22';
            document.getElementById('clusterDescription').value = '';
        }

        async function testConnection() {
            const name = document.getElementById('clusterName').value;
            const host = document.getElementById('clusterHost').value;
            const username = document.getElementById('clusterUsername').value;
            const password = document.getElementById('clusterPassword').value;
            const port = document.getElementById('clusterPort').value || 22;

            if (!host || !username || !password) {
                showNotification('Please fill in host, username, and password', 'error');
                return;
            }

            try {
                showNotification('Testing connection...', 'info');

                const response = await fetch('/api/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        host: host,
                        username: username,
                        password: password,
                        port: parseInt(port)
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Connection successful! ${result.message}`, 'success');
                } else {
                    showNotification(`❌ Connection failed: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error testing connection:', error);
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function saveCluster() {
            const name = document.getElementById('clusterName').value;
            const host = document.getElementById('clusterHost').value;
            const username = document.getElementById('clusterUsername').value;
            const password = document.getElementById('clusterPassword').value;
            const port = document.getElementById('clusterPort').value || 22;
            const description = document.getElementById('clusterDescription').value;

            if (!name || !host || !username || !password) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            try {
                showNotification('Saving cluster...', 'info');

                const response = await fetch('/api/clusters', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: name,
                        host: host,
                        username: username,
                        password: password,
                        port: parseInt(port),
                        description: description,
                        enabled: true
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Cluster '${name}' saved successfully!`, 'success');
                    closeModal('clusterModal');
                    loadClusters(); // Refresh cluster list

                    // Auto-select the new cluster
                    setTimeout(() => {
                        const select = document.getElementById('clusterSelect');
                        select.value = result.cluster_id;
                        loadClusterData(result.cluster_id);
                    }, 1000);
                } else {
                    showNotification(`❌ Failed to save cluster: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error saving cluster:', error);
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function loadClusterManagement() {
            const container = document.getElementById('clusterList');
            container.innerHTML = '<div class="loading">Loading clusters...</div>';

            try {
                const response = await fetch('/api/clusters');
                const clusters = await response.json();

                if (clusters.length === 0) {
                    container.innerHTML = '<div class="loading">No clusters configured</div>';
                    return;
                }

                let html = '';
                clusters.forEach(cluster => {
                    const statusClass = cluster.status === 'online' ? 'online' : 'offline';
                    html += `
                        <div class="cluster-item">
                            <div class="cluster-item-header">
                                <span class="cluster-item-name">${escapeHtml(cluster.name)}</span>
                                <span class="cluster-item-status ${statusClass}">${cluster.status}</span>
                            </div>
                            <div class="cluster-item-info">
                                <strong>Host:</strong> ${escapeHtml(cluster.host)} |
                                <strong>User:</strong> ${escapeHtml(cluster.username)} |
                                <strong>Jobs:</strong> ${cluster.running_count} running, ${cluster.queue_count} queued
                            </div>
                            <div class="cluster-item-info">
                                ${escapeHtml(cluster.description || 'No description')}
                            </div>
                            <div class="cluster-item-actions">
                                <button class="btn btn-primary" onclick="selectCluster('${cluster.id}')">📊 Select</button>
                                <button class="btn btn-danger" onclick="deleteCluster('${cluster.id}', '${escapeHtml(cluster.name)}')">🗑️ Delete</button>
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;

            } catch (error) {
                console.error('Error loading cluster management:', error);
                container.innerHTML = '<div class="error">Error loading clusters</div>';
            }
        }

        function selectCluster(clusterId) {
            const select = document.getElementById('clusterSelect');
            select.value = clusterId;
            loadClusterData(clusterId);
            closeModal('clusterManagementModal');
            showNotification('Cluster selected', 'success');
        }

        async function deleteCluster(clusterId, clusterName) {
            if (!confirm(`Are you sure you want to delete cluster '${clusterName}'?\n\nThis action cannot be undone.`)) {
                return;
            }

            try {
                showNotification(`Deleting cluster '${clusterName}'...`, 'info');

                const response = await fetch(`/api/clusters/${clusterId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ Cluster '${clusterName}' deleted successfully`, 'success');
                    loadClusterManagement(); // Refresh the list
                    loadClusters(); // Refresh the dropdown

                    // If this was the current cluster, clear the display
                    if (currentCluster === clusterId) {
                        currentCluster = null;
                        document.getElementById('clusterInfo').style.display = 'none';
                    }
                } else {
                    showNotification(`❌ Failed to delete cluster: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('Error deleting cluster:', error);
                showNotification(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const clusterModal = document.getElementById('clusterModal');
            const managementModal = document.getElementById('clusterManagementModal');

            if (event.target === clusterModal) {
                closeModal('clusterModal');
            }
            if (event.target === managementModal) {
                closeModal('clusterManagementModal');
            }
        }

        // Helper Functions for Save Order Section
        function showOrderSaveSection() {
            const section = document.getElementById('saveOrderSection');
            const info = document.getElementById('orderChangeInfo');

            // Show what changed
            const changeInfo = getOrderChangeDescription();
            info.textContent = changeInfo;

            section.style.display = 'flex';
        }

        function hideOrderSaveSection() {
            const section = document.getElementById('saveOrderSection');
            section.style.display = 'none';
        }

        function getOrderChangeDescription() {
            return getDetailedChangeDescription(originalQueueOrder, pendingQueueOrder);
        }

        function getDetailedChangeDescription(originalOrder, newOrder) {
            if (originalOrder.length === 0 || newOrder.length === 0) {
                return 'Order changed';
            }

            // Find what moved
            const changes = [];
            for (let i = 0; i < newOrder.length; i++) {
                const jobId = newOrder[i];
                const originalPos = originalOrder.indexOf(jobId);
                if (originalPos !== i && originalPos !== -1) {
                    const shortJobId = jobId.split('_')[0];
                    changes.push(`Job ${shortJobId} moved from position ${originalPos + 1} to ${i + 1}`);
                }
            }

            if (changes.length === 0) {
                return 'Order changed';
            } else if (changes.length === 1) {
                return changes[0];
            } else {
                return `${changes.length} jobs moved: ${changes.join(', ')}`;
            }
        }

        function arraysEqual(a, b) {
            if (a.length !== b.length) return false;
            for (let i = 0; i < a.length; i++) {
                if (a[i] !== b[i]) return false;
            }
            return true;
        }

        async function saveQueueOrder() {
            if (!hasUnsavedChanges || pendingQueueOrder.length === 0) {
                showNotification('No changes to save', 'info');
                return;
            }

            console.log('Saving queue order:', pendingQueueOrder);
            showNotification('Applying new queue order...', 'info');

            // Hide the save section
            hideOrderSaveSection();

            // Apply the new order using the reverse scontrol top logic
            await applyQueueOrder(pendingQueueOrder);

            // Reset state
            hasUnsavedChanges = false;
        }

        function cancelOrderChange() {
            console.log('Canceling order changes, reverting to original');

            // Revert to original order visually
            hasUnsavedChanges = false;
            hideOrderSaveSection();

            // Reload the data to show original order
            loadClusterData(currentCluster);

            showNotification('Changes canceled, reverted to original order', 'info');
        }

        // Requeue functionality
        function showRequeueConfirmation(baseJobId, displayJobId, jobName, runtime, jobCount) {
            const jobText = jobCount === 1 ? 'job' : 'jobs';
            const message = `
⚠️ REQUEUE CONFIRMATION ⚠️

You are about to requeue running ${jobText}:
• Job ID: ${displayJobId}
• Name: ${jobName}
• Current Runtime: ${runtime}
• Number of ${jobText}: ${jobCount}

🔄 IMPORTANT RECOMMENDATION:
Before requeuing, consider adding your pending jobs to the queue first.
This ensures your requeued ${jobText} will have higher priority when restarted.

ℹ️ REQUEUE PROCESS:
1. All ${jobCount} ${jobText} will be requeued using 'scontrol requeue'
2. Then released using 'scontrol release' to start immediately

❓ Do you want to proceed with requeuing ${jobCount} ${jobText}?

✅ Click OK to requeue
❌ Click Cancel to abort`;

            if (confirm(message)) {
                requeueJob(baseJobId, displayJobId, jobCount);
            }
        }

        async function requeueJob(baseJobId, displayJobId, jobCount) {
            if (!currentCluster) {
                showNotification('No cluster selected', 'error');
                return;
            }

            try {
                showLoading();
                const jobText = jobCount === 1 ? 'job' : 'jobs';
                showNotification(`Requeuing ${jobCount} ${jobText} for ${displayJobId}...`, 'info');

                const response = await fetch(`/api/cluster/${currentCluster}/requeue_job`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job_id: baseJobId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`✅ ${result.message}`, 'success');
                    // Refresh data to show updated job status
                    setTimeout(() => {
                        loadClusterData(currentCluster);
                    }, 3000); // Longer delay for requeue process
                } else {
                    showNotification(`❌ Requeue failed: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('Error requeuing job:', error);
                showNotification('Error requeuing job: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        console.log('Enhanced Dashboard JavaScript loaded successfully');
    </script>
</body>
</html>
