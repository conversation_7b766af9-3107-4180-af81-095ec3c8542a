<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLURM Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .cluster-selector {
            margin: 20px 0;
        }
        .cluster-selector select {
            padding: 10px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
            margin-right: 10px;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .jobs-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .jobs-table th,
        .jobs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .jobs-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .jobs-table tr:hover {
            background-color: #f8f9fa;
        }
        .loading {
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }
        .status-online { color: #27ae60; }
        .status-offline { color: #e74c3c; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            display: none;
        }
        .notification.success { background: #27ae60; }
        .notification.error { background: #e74c3c; }
        .notification.info { background: #3498db; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🖥️ SLURM Dashboard</h1>
        <p>Real-time cluster monitoring</p>
    </div>

    <div class="cluster-selector">
        <label for="clusterSelect">Select Cluster:</label>
        <select id="clusterSelect">
            <option value="">Loading clusters...</option>
        </select>
        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
    </div>

    <div id="clusterInfo" style="display: none;">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Cluster Status</h3>
                <div id="clusterStatus" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Running Jobs</h3>
                <div id="runningCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Queue Jobs</h3>
                <div id="queueCount" class="stat-number">-</div>
            </div>
            <div class="stat-card">
                <h3>Total Nodes</h3>
                <div id="nodeCount" class="stat-number">-</div>
            </div>
        </div>

        <div class="jobs-section">
            <h3>🏃 Running Jobs</h3>
            <div id="runningJobsContent">
                <div class="loading">Loading running jobs...</div>
            </div>
        </div>

        <div class="jobs-section">
            <h3>⏳ Queue Jobs</h3>
            <div id="queueJobsContent">
                <div class="loading">Loading queue jobs...</div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        console.log('Dashboard JavaScript starting...');
        
        let currentCluster = null;

        // Show that JavaScript is working
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing dashboard...');
            showNotification('Dashboard loading...', 'info');
            loadClusters();
        });

        async function loadClusters() {
            console.log('Loading clusters...');
            try {
                const response = await fetch('/api/clusters');
                console.log('Clusters response:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const clusters = await response.json();
                console.log('Clusters data:', clusters);
                
                const select = document.getElementById('clusterSelect');
                select.innerHTML = '';
                
                if (clusters.length === 0) {
                    select.innerHTML = '<option value="">No clusters configured</option>';
                    showNotification('No clusters found', 'error');
                    return;
                }
                
                select.innerHTML = '<option value="">Select a cluster...</option>';
                clusters.forEach(cluster => {
                    const option = document.createElement('option');
                    option.value = cluster.id;
                    option.textContent = `${cluster.name} (${cluster.host})`;
                    select.appendChild(option);
                });
                
                // Auto-select first cluster
                if (clusters.length > 0) {
                    select.value = clusters[0].id;
                    loadClusterData(clusters[0].id);
                }
                
                showNotification(`Found ${clusters.length} cluster(s)`, 'success');
                
            } catch (error) {
                console.error('Error loading clusters:', error);
                document.getElementById('clusterSelect').innerHTML = '<option value="">Error loading clusters</option>';
                showNotification('Failed to load clusters: ' + error.message, 'error');
            }
        }

        async function loadClusterData(clusterId) {
            if (!clusterId) {
                document.getElementById('clusterInfo').style.display = 'none';
                return;
            }

            console.log('Loading data for cluster:', clusterId);
            currentCluster = clusterId;
            document.getElementById('clusterInfo').style.display = 'block';
            
            // Show loading state
            document.getElementById('clusterStatus').textContent = 'Loading...';
            document.getElementById('runningCount').textContent = '-';
            document.getElementById('queueCount').textContent = '-';
            document.getElementById('nodeCount').textContent = '-';

            try {
                const response = await fetch(`/api/cluster/${clusterId}/data`);
                console.log('Cluster data response:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Cluster data:', data);
                
                if (data.error) {
                    throw new Error(data.error);
                }

                // Update stats
                document.getElementById('clusterStatus').textContent = data.status || 'Unknown';
                document.getElementById('clusterStatus').className = `stat-number status-${data.status}`;
                document.getElementById('runningCount').textContent = data.running_jobs ? data.running_jobs.length : 0;
                document.getElementById('queueCount').textContent = data.queue_jobs ? data.queue_jobs.length : 0;
                document.getElementById('nodeCount').textContent = data.cluster_info?.nodes?.total || 0;

                // Update running jobs
                updateJobsTable('runningJobsContent', data.running_jobs || [], 'running');
                
                // Update queue jobs
                updateJobsTable('queueJobsContent', data.queue_jobs || [], 'queue');

                showNotification('Data loaded successfully', 'success');

            } catch (error) {
                console.error('Error loading cluster data:', error);
                showNotification('Failed to load cluster data: ' + error.message, 'error');
            }
        }

        function updateJobsTable(containerId, jobs, type) {
            const container = document.getElementById(containerId);
            
            if (jobs.length === 0) {
                container.innerHTML = '<div class="loading">No jobs found</div>';
                return;
            }

            let tableHTML = '<table class="jobs-table"><thead><tr>';
            
            if (type === 'running') {
                tableHTML += '<th>Job ID</th><th>Name</th><th>User</th><th>Time</th><th>Nodes</th>';
            } else {
                tableHTML += '<th>Job ID</th><th>Name</th><th>User</th><th>Priority</th><th>Reason</th>';
            }
            
            tableHTML += '</tr></thead><tbody>';
            
            // Show first 10 jobs
            jobs.slice(0, 10).forEach(job => {
                tableHTML += '<tr>';
                tableHTML += `<td>${job.job_id}</td>`;
                tableHTML += `<td>${job.name}</td>`;
                tableHTML += `<td>${job.user}</td>`;
                
                if (type === 'running') {
                    tableHTML += `<td>${job.time || '-'}</td>`;
                    tableHTML += `<td>${job.nodes || '-'}</td>`;
                } else {
                    tableHTML += `<td>${job.priority || '-'}</td>`;
                    tableHTML += `<td>${job.reason || '-'}</td>`;
                }
                
                tableHTML += '</tr>';
            });
            
            tableHTML += '</tbody></table>';
            
            if (jobs.length > 10) {
                tableHTML += `<p><em>Showing first 10 of ${jobs.length} jobs</em></p>`;
            }
            
            container.innerHTML = tableHTML;
        }

        function refreshData() {
            if (currentCluster) {
                loadClusterData(currentCluster);
            } else {
                loadClusters();
            }
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }

        // Handle cluster selection
        document.getElementById('clusterSelect').addEventListener('change', function(e) {
            loadClusterData(e.target.value);
        });

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (currentCluster) {
                loadClusterData(currentCluster);
            }
        }, 30000);

        console.log('Dashboard JavaScript loaded successfully');
    </script>
</body>
</html>
