# SLURM Dashboard

A professional web interface for SLURM cluster management with real-time queue monitoring and priority adjustment capabilities.

## Features

### 🎯 Core Functionality
- **Real-time Queue Monitoring**: Live view of pending jobs with automatic updates
- **Drag-and-Drop Priority Management**: Intuitive priority adjustment by dragging jobs in the queue
- **Comprehensive Cluster Overview**: Node status, resource utilization, and partition information
- **Running Jobs Display**: Monitor currently executing jobs
- **Professional UI**: Modern, responsive design with real-time updates

### 🔧 Technical Features
- **WebSocket Integration**: Real-time updates without page refresh
- **SLURM Command Integration**: Direct interface with SLURM commands
- **Sudo Support**: Configurable privileged user for priority changes
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Error Handling**: Robust error handling and user notifications

## Installation

### 🐳 Docker Deployment (Recommended)

The easiest way to deploy the dashboard is using Docker, which allows you to run it on any machine while connecting to your SLURM cluster via SSH.

#### Prerequisites
- Docker and Docker Compose
- SSH access to SLURM head node
- Web browser with modern JavaScript support

#### Quick Start

1. **Setup SSH and configuration**
   ```bash
   ./docker-setup.sh
   ```

2. **Edit environment file**
   ```bash
   # Edit .env with your SLURM host details
   SLURM_HOST=your-slurm-head-node.example.com
   SLURM_USER=slurmadmin
   CLUSTER_NAME=Production HPC Cluster
   ```

3. **Deploy with Docker Compose**
   ```bash
   docker-compose up --build -d
   ```

4. **Access dashboard**
   Open `http://localhost:5000` in your browser

📖 **For detailed Docker deployment instructions, see [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)**

### 🖥️ Local Installation

For running directly on the SLURM head node:

#### Prerequisites
- Python 3.7+
- SLURM cluster access
- Web browser with modern JavaScript support

#### Setup Steps

1. **Clone or download the project files**
   ```bash
   cd /path/to/slurm-dashboard
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the application**
   Edit `config.py` to match your environment:
   ```python
   # SLURM settings
   SLURM_USER = 'your-slurm-admin-user'  # User with sudo privileges
   CLUSTER_NAME = 'Your Cluster Name'
   ```

4. **Set up sudo permissions (if needed)**
   Add the following to `/etc/sudoers` for the web server user:
   ```
   webuser ALL=(slurm-admin-user) NOPASSWD: /usr/bin/scontrol
   ```

## Usage

### Starting the Dashboard

1. **Run the application**
   ```bash
   python main.py
   ```

2. **Access the dashboard**
   Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```
   Or from other machines:
   ```
   http://your-server-ip:5000
   ```

### Dashboard Features

#### Left Panel - Queue Management
- **View Pending Jobs**: All jobs in queue with priority indicators
- **Drag to Reorder**: Drag jobs up/down to change priority
- **Color Coding**:
  - 🔴 Red: High priority (≥1000)
  - 🟡 Yellow: Medium priority (100-999)
  - ⚪ Gray: Low priority (<100)
- **Job Details**: Click any job to view detailed information

#### Main Dashboard
- **Cluster Overview**:
  - Node status (idle, allocated, mixed, down)
  - Total CPU and memory resources
  - Partition information
- **Running Jobs Table**: Currently executing jobs with runtime info

#### Real-time Updates
- Automatic refresh every 5 seconds
- Manual refresh button available
- WebSocket connection status indicator

## Configuration

### Environment Variables
```bash
export SLURM_USER="slurmadmin"
export CLUSTER_NAME="HPC Production Cluster"
export SECRET_KEY="your-secret-key"
```

### Config File Options
Edit `config.py`:
```python
class Config:
    # Flask settings
    SECRET_KEY = 'your-secret-key'
    DEBUG = True  # Set to False in production

    # SLURM settings
    SLURM_USER = 'slurmadmin'
    REFRESH_INTERVAL = 5  # seconds

    # Dashboard limits
    MAX_QUEUE_JOBS = 100
    MAX_RUNNING_JOBS = 50

    # Cluster information
    CLUSTER_NAME = 'Your Cluster Name'
```

## API Endpoints

The dashboard provides REST API endpoints:

- `GET /api/queue_jobs` - Get pending jobs
- `GET /api/running_jobs` - Get running jobs
- `GET /api/cluster_info` - Get cluster information
- `POST /api/update_priority` - Update job priority
- `GET /api/job_details/<job_id>` - Get job details

## Security Considerations

### Production Deployment
1. **Set DEBUG = False** in config.py
2. **Use HTTPS** with proper SSL certificates
3. **Configure firewall** to restrict access
4. **Set strong SECRET_KEY**
5. **Limit sudo permissions** to specific commands only

### Recommended Sudo Configuration
```bash
# /etc/sudoers.d/slurm-dashboard
webuser ALL=(slurmadmin) NOPASSWD: /usr/bin/scontrol update JobId=* Priority=*
webuser ALL=(slurmadmin) NOPASSWD: /usr/bin/squeue *
webuser ALL=(slurmadmin) NOPASSWD: /usr/bin/sinfo *
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check if SLURM commands are accessible
   - Verify sudo permissions
   - Check firewall settings

2. **Priority Changes Not Working**
   - Verify SLURM_USER has appropriate permissions
   - Check sudo configuration
   - Review application logs

3. **No Data Displayed**
   - Ensure SLURM is running
   - Check command paths in slurm_manager.py
   - Verify network connectivity

### Logs and Debugging
- Enable debug mode in config.py
- Check browser console for JavaScript errors
- Monitor server logs for Python errors

## File Structure

```
slurm-dashboard/
├── main.py              # Application entry point
├── app.py               # Flask application
├── slurm_manager.py     # SLURM command interface
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
├── templates/
│   └── dashboard.html   # Main dashboard template
├── static/
│   ├── css/
│   │   └── dashboard.css # Professional styling
│   └── js/
│       └── dashboard.js  # Interactive functionality
└── README.md           # This file
```

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

This project is provided as-is for educational and operational purposes.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review server and browser logs
3. Verify SLURM configuration and permissions
